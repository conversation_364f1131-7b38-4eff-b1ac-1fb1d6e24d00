package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.business.domain.vo.ExperimentProjectDataVO;
import com.huatek.frame.modules.business.domain.vo.ExperimentProjectVO;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.service.ExperimentProjectDataService;
import com.huatek.frame.modules.business.service.dto.ExperimentProjectDataDTO;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.service.SysGroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.ExperimentProject;
import com.huatek.frame.modules.business.service.ExperimentProjectService;
import com.huatek.frame.modules.business.service.dto.ExperimentProjectDTO;
import java.sql.Date;
import java.util.stream.Collectors;

import com.huatek.frame.modules.business.domain.StandardProcessManagement;
import com.huatek.frame.modules.business.domain.DeviceType;
import com.huatek.frame.modules.system.domain.SysGroup;
import org.springframework.util.CollectionUtils;



/**
 * 试验项目 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "experimentProject")
//@RefreshScope
@Slf4j
public class ExperimentProjectServiceImpl implements ExperimentProjectService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private ExperimentProjectMapper experimentProjectMapper;

    @Autowired
    private ExperimentProjectDataService experimentProjectDataService;

    @Autowired
    private ExperimentProjectDataMapper experimentProjectDataMapper;

	@Autowired
    private StandardProcessManagementMapper standardProcessManagementMapper;
	@Autowired
    private DeviceTypeMapper deviceTypeMapper;
	@DubboReference
    private SysGroupService sysGroupService;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public ExperimentProjectServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ExperimentProjectVO>> findExperimentProjectPage(ExperimentProjectDTO dto) {

        if(!HuatekTools.isEmpty(dto.getGrouping())) {
           String experimentProject = dto.getGrouping().replaceAll(",", "|");
           experimentProject = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", experimentProject);
           dto.setGrouping(experimentProject);
        }
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ExperimentProjectVO> experimentProjects = experimentProjectMapper.selectExperimentProjectPage(dto);
		TorchResponse<List<ExperimentProjectVO>> response = new TorchResponse<List<ExperimentProjectVO>>();
		response.getData().setData(experimentProjects);
		response.setStatus(200);
		response.getData().setCount(experimentProjects.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ExperimentProjectDTO experimentProjectDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(experimentProjectDto.getCodexTorchDeleted())) {
            experimentProjectDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = experimentProjectDto.getId();
		ExperimentProject entity = new ExperimentProject();
        BeanUtils.copyProperties(experimentProjectDto, entity);
        StandardProcessManagement  standardProcessManagement =standardProcessManagementMapper.selectById(experimentProjectDto.getProcessId());
        entity.setProcessCode3(standardProcessManagement.getStepNumber());
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			experimentProjectMapper.insert(entity);
            if(null !=experimentProjectDto.getProjectDataItems()){
                List<ExperimentProjectDataDTO> projectDatas = Arrays.asList(experimentProjectDto.getProjectDataItems());
                for (ExperimentProjectDataDTO experimentProjectDataDto : projectDatas){
                    experimentProjectDataDto.setId("");
                    //非必要字段处理
                    experimentProjectDataDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
                    //主子表关联ID
                    experimentProjectDataDto.setCodexTorchMasterFormId(entity.getId());
                    // 业务字段管理
                    experimentProjectDataDto.setStandardProcessCode(entity.getProcessCode3());
                    //提交
                    experimentProjectDataService.saveOrUpdate(experimentProjectDataDto);
                }
            }
		} else {
			experimentProjectMapper.updateById(entity);
            if(null !=experimentProjectDto.getProjectDataItems()) {
                List<ExperimentProjectDataDTO> projectDatas = Arrays.asList(experimentProjectDto.getProjectDataItems());
                List<String> ids = projectDatas.stream().map(ExperimentProjectDataDTO::getId).collect(Collectors.toList());
                for (ExperimentProjectDataDTO experimentProjectDataDto : projectDatas) {
                    experimentProjectDataDto.setId(experimentProjectDataDto.getId());
                    //非必要字段处理
                    experimentProjectDataDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
                    //主子表关联ID
                    experimentProjectDataDto.setCodexTorchMasterFormId(entity.getId());
                    // 业务字段管理
                    experimentProjectDataDto.setStandardProcessCode(entity.getProcessCode3());
                    //提交
                    experimentProjectDataService.saveOrUpdate(experimentProjectDataDto);
                }
                if(null!=ids && ids.size()>0){
                    QueryWrapper wrapper = new QueryWrapper();
                    wrapper.notIn("id", ids);
                    experimentProjectDataMapper.delete(wrapper);
                }
            }
		}

		TorchResponse response = new TorchResponse();
        ExperimentProjectVO vo = new ExperimentProjectVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ExperimentProjectVO> findExperimentProject(String id) {
		ExperimentProjectVO vo = new ExperimentProjectVO();
		if (!HuatekTools.isEmpty(id)) {
			ExperimentProject entity = experimentProjectMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
            ExperimentProjectDataDTO dto = new ExperimentProjectDataDTO();
            dto.setCodexTorchMasterFormId(entity.getId());
            List<ExperimentProjectDataVO> datavos =  experimentProjectDataMapper.selectExperimentProjectDataList(dto);
			BeanUtils.copyProperties(entity, vo);
            vo.setProjectDataItems(datavos);
		}
		TorchResponse<ExperimentProjectVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		experimentProjectMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
            //初始化外键函数
            selectOptionsFuncMap.put("processCode3",experimentProjectMapper::selectOptionsByProcessCode3);
            //初始化外键函数
            selectOptionsFuncMap.put("processId",experimentProjectMapper::selectOptionsByProcessCode3);
            //初始化外键函数
            selectOptionsFuncMap.put("assoWoPredProc",experimentProjectMapper::selectOptionsByAssoWoPredProc);
            //初始化外键函数
            selectOptionsFuncMap.put("workstation",experimentProjectMapper::selectOptionsByWorkstation);
//            selectOptionsFuncMap.put("productModel",experimentProjectMapper::selectOptionsByProductModel);
            //初始化外键函数
            selectOptionsFuncMap.put("productInformation1",experimentProjectMapper::selectOptionsByProductInformation1);
            //初始化外键函数
            selectOptionsFuncMap.put("testingTeam",experimentProjectMapper::selectOptionsByTestingTeam);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}




    @Override
    public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
        Map<String, String> data = new HashMap();
        try {
            switch (linkageDataTableName) {
                case "standard_process_management":
                    data = selectDataLinkageByProcessCode3(conditionalValue);
                    break;
                case "workstation":
                    data = selectDataLinkageByWorkstation(conditionalValue);
                    break;
//                case "product_management":
//                    data = selectDataLinkageByProductModel(conditionalValue);
//                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
    }
    @Override
    public Map<String,String> selectDataLinkageByProcessCode3(String step_number) {
        return experimentProjectMapper.selectDataLinkageByProcessId(step_number);
    }
    @Override
    public Map<String,String> selectDataLinkageByWorkstation(String workstation_number) {
        return experimentProjectMapper.selectDataLinkageByWorkstation(workstation_number);
    }

//    @Override
//    public Map<String,String> selectDataLinkageByProductModel(String productModel) {
//        return experimentProjectMapper.selectDataLinkageByProductModel(productModel);
//    }

    @Override
    @ExcelExportConversion(tableName = "experiment_project", convertorFields = "testMethodology,grouping,whetherToIncludeInScheduling")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ExperimentProjectVO> selectExperimentProjectList(ExperimentProjectDTO dto) {
        return experimentProjectMapper.selectExperimentProjectList(dto);
    }

    /**
     * 导入试验项目数据
     *
     * @param experimentProjectList 试验项目数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "experiment_project", convertorFields = "testMethodology,grouping,whetherToIncludeInScheduling")
    public TorchResponse importExperimentProject(List<ExperimentProjectVO> experimentProjectList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(experimentProjectList) || experimentProjectList.size() == 0) {
            throw new ServiceException("导入试验项目数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ExperimentProjectVO vo : experimentProjectList) {
            try {
                ExperimentProject experimentProject = new ExperimentProject();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, experimentProject);
                QueryWrapper<ExperimentProject> wrapper = new QueryWrapper();
                ExperimentProject oldExperimentProject = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = ExperimentProjectVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<ExperimentProject> oldExperimentProjectList = experimentProjectMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldExperimentProjectList) && oldExperimentProjectList.size() > 1) {
                        experimentProjectMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldExperimentProjectList) && oldExperimentProjectList.size() == 1) {
                        oldExperimentProject = oldExperimentProjectList.get(0);
                    }
                }
                if (StringUtils.isNull(oldExperimentProject)) {
                    BeanValidators.validateWithException(validator, vo);
                    experimentProjectMapper.insert(experimentProject);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工序方案名称 " + vo.getProcessSchemeName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldExperimentProject, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    experimentProjectMapper.updateById(oldExperimentProject);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工序方案名称 " + vo.getProcessSchemeName() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、工序方案名称 " + vo.getProcessSchemeName() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、工序方案名称 " + vo.getProcessSchemeName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(ExperimentProjectVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getProcessSchemeName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工序方案名称不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getProcessCode3())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工序编码不能为空!");
        }
        if (!HuatekTools.isEmpty(vo.getProcessCode3())) {
            List<String> processCode3List = Arrays.asList(vo.getProcessCode3().split(","));
            List<StandardProcessManagement> list = standardProcessManagementMapper.selectList(new QueryWrapper<StandardProcessManagement>().in("step_number", processCode3List));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("工序编码=" + vo.getProcessCode3() + "; ");
            }
        }
        if (!HuatekTools.isEmpty(vo.getAssoWoPredProc())) {
            List<String> assoWoPredProcList = Arrays.asList(vo.getAssoWoPredProc().split(","));
            List<StandardProcessManagement> list = standardProcessManagementMapper.selectList(new QueryWrapper<StandardProcessManagement>().in("step_number", assoWoPredProcList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("关联工单前置工序=" + vo.getAssoWoPredProc() + "; ");
            }
        }

        if (HuatekTools.isEmpty(vo.getTestMethodology())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>试验方式不能为空!");
        }
//        if (!HuatekTools.isEmpty(vo.getTestingTeam())) {
//            List<String> testingTeamList = Arrays.asList(vo.getTestingTeam().split(","));
//            sysGroupService.findGroupsByCode(testingTeamList)
//            List<SysGroup> list = sysGroupMapper.selectList(new QueryWrapper<SysGroup>().in("group_code", testingTeamList));
//            if (CollectionUtils.isEmpty(list)) {
//                failureRecord++;
//                failureRecordMsg.append("试验班组=" + vo.getTestingTeam() + "; ");
//            }
//        }
        if (HuatekTools.isEmpty(vo.getWhetherToIncludeInScheduling())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>是否加入排产不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getCodexTorchUpdater())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>更新人不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectExperimentProjectListByIds(List<String> ids) {
        List<ExperimentProjectVO> experimentProjectList = experimentProjectMapper.selectExperimentProjectListByIds(ids);

		TorchResponse<List<ExperimentProjectVO>> response = new TorchResponse<List<ExperimentProjectVO>>();
		response.getData().setData(experimentProjectList);
		response.setStatus(200);
		response.getData().setCount((long)experimentProjectList.size());
		return response;
    }



}
