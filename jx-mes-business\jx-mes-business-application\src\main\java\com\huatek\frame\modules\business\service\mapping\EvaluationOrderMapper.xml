<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.EvaluationOrderMapper">
	<sql id="Base_Column_List">
		t.id as id,
        t.customer_id as customerId,
		t.order_number as orderNumber,
		t.order_type as orderType,
		t.entrusted_unit as entrustedUnit,
<!--		u.settlement_unit as settlementUnit,-->
<!--		u.customer_manager as customerManager,-->
		t.principal as principal,
		t.principals_phone_number as principalsPhoneNumber,
		t.date_of_entrustment as dateOfEntrustment,
		t.deadline as deadline,
        t.complete_date as completeDate,
		t.urgency_level as urgencyLevel,
		t.engineering_code as engineeringCode,
		t.order_inspection_number as orderInspectionNumber,
		t.report_requirements as reportRequirements,
		t.report_format as reportFormat,
		t.data_format as dataFormat,
		t.data_req_e_rep as dataReqERep,
		t.data_reqs_papereport as dataReqsPapereport,
		t.other_requirements as otherRequirements,
		t.`comment` as `comment`,
		t.upload_attachment as uploadAttachment,
		t.status as status,
		t.production_stage as productionStage,
		t.test_methodology as testMethodology,
		t.acceptance_notice as acceptanceNotice,
		t.purchase_order_contract_number as purchaseOrderContractNumber,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
    <sql id="Product_List">
        p.id as productListId,
        p.evaluation_order_id as evaluationOrderId,
        p.standard_specification_id as standardSpecificationId,
        p.product_model as productModel,
        p.manufacturer as manufacturer,
        p.product_category as productCategory,
        p.product_name as productName,
        p.serial_number as serialNumber,
        p.product_model as productModel,
        p.production_batch as productionBatch,
        p.quantity as quantity,
        p.task_level as taskLevel,
        p.test_type as testType,
        p.work_order_inspection_number as workOrderInspectionNumber,
        p.quality_grade as qualityGrade,
        p.sample_total_count as sampleTotalCount,
        p.experiment_project as experimentProject,
        p.special_analysis_test_project as specialAnalysisTestProject,
        p.group_type as groupType,
        p.inspection_test_project as inspectionTestProject,
        p.quality_consistency_test_items as qualityConsistencyTestItems,
        p.dpa_test_project as dpaTestProject,
        p.other_tests as otherTests,
        p.technical_liaison_name as technicalLiaisonName,
        p.technical_liaison_phone_number as technicalLiaisonPhoneNumber,
        p.deadline as productListDeadline,
        p.status as status,
        p.reject_unscreenable_reason as rejectUnscreenableReason
    </sql>
    <sql id="Customer_Infomation_Managemant">
        c.entrusted_unit as entrustedUnitName,
        c.settlement_unit as settlementUnit,
        c.customer_manager as customerManager
    </sql>
    <resultMap id="evalueationOrderQueryResultMap" type="com.huatek.frame.modules.business.domain.vo.EvaluationOrderQueryRespVO">
        <association property="evaluationOrderVO" javaType="com.huatek.frame.modules.business.domain.vo.EvaluationOrderVO">
            <id property="id" column="evaluationOrderId" />
            <result property="customerId" column="customerId"/>
            <result property="entrustedUnitName" column="entrustedUnitName"/>
            <result property="orderNumber" column="orderNumber" />
            <result property="orderType" column="orderType"/>
            <result property="dateOfEntrustment" column="dateOfEntrustment"/>
            <result property="urgencyLevel" column="urgencyLevel"/>
            <result property="deadline" column="evaluationOrderDeadline"/>
            <result property="status" column="status"/>
            <result property="productionStage" column="productionStage"/>
            <result property="customerManager" column="customerManager"/>
            <result property="codexTorchCreateDatetime" column="codexTorchCreateDatetime"/>
            <result property="codexTorchUpdater" column="codexTorchUpdater"/>
        </association>

        <collection property="productListVOList" ofType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
            <id property="id" column="productListId"/>
            <result property="serialNumber" column="serialNumber"/>
            <result property="testType" column="testType"/>
            <result property="deadline" column="productListDeadline"/>
            <result property="productName" column="productName"/>
            <result property="productModel" column="productModel"/>
            <result property="productionBatch" column="productionBatch"/>
            <result property="quantity" column="quantity"/>
            <result property="manufacturer" column="manufacturer"/>
            <result property="qualityGrade" column="qualityGrade"/>
            <result property="standardSpecificationId" column="standardSpecificationId"/>
            <result property="taskLevel" column="taskLevel"/>
            <result property="workOrderInspectionNumber" column="workOrderInspectionNumber"/>
            <result property="rejectUnscreenableReason" column="rejectUnscreenableReason"/>
        </collection>


    </resultMap>
	<select id="selectEvaluationOrderPage" parameterType="com.huatek.frame.modules.business.service.dto.EvaluationOrderDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.EvaluationOrderVO">
		select
		<include refid="Base_Column_List" />,
        <include refid="Customer_Infomation_Managemant"/>,
        p.product_model as productModel,
        p.manufacturer as manufacturer,
        p.product_name as productName,
        p.test_type as testType,
        s.specification_number as standardSpecificationId
			from evaluation_order t
            LEFT JOIN customer_information_management c ON c.id = t.customer_id
            LEFT JOIN product_list p on p.evaluation_order_id = t.id
            LEFT JOIN standard_specification s on p.standard_specification_id = s.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="orderType != null and orderType != ''">
                    and t.order_type REGEXP #{orderType}
                </if>
                <if test="entrustedUnitName != null and entrustedUnitName != ''">
                    and c.entrusted_unit like concat('%', #{entrustedUnitName}, '%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit like concat('%', #{entrustedUnit}, '%')
                </if>
                <if test="settlementUnit != null and settlementUnit != ''">
                    and c.settlement_unit like concat('%', #{settlementUnit}, '%')
                </if>
                <if test="customerManager != null and customerManager != ''">
                    and c.customer_manager like concat('%', #{customerManager}, '%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and p.product_model like concat('%', #{productModel}, '%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and p.manufacturer like concat('%', #{manufacturer}, '%')
                </if>
                <if test="productName != null and productName != ''">
                    and p.product_name like concat('%', #{productName}, '%')
                </if>
                <if test="testType != null and testType != ''">
                    and p.test_type like concat('%', #{testType}, '%')
                </if>
                <if test="principal != null and principal != ''">
                    and t.principal  like concat('%', #{principal} ,'%')
                </if>
                <if test="principalsPhoneNumber != null and principalsPhoneNumber != ''">
                    and t.principals_phone_number  like concat('%', #{principalsPhoneNumber} ,'%')
                </if>
<!--                <if test="dateOfEntrustment != null">-->
<!--                    and t.date_of_entrustment  = #{dateOfEntrustment}-->
<!--                </if>-->
<!--                <if test="deadline != null">-->
<!--                    and t.deadline  = #{deadline}-->
<!--                </if>-->
                <!-- 委托日期区间查询条件 -->
                <!-- 委托日期区间查询条件 -->
                <if test="startDateOfEntrustment != null">
                    and t.date_of_entrustment &gt;= #{startDateOfEntrustment}
                </if>
                <if test="endDateOfEntrustment != null">
                    and t.date_of_entrustment &lt;= #{endDateOfEntrustment}
                </if>

                <!-- 要求完成日期区间查询条件 -->
                <if test="startDateOfDeadline != null">
                    and t.deadline &gt;= #{startDateOfDeadline}
                </if>
                <if test="endDateOfDeadline != null">
                    and t.deadline &lt;= #{endDateOfDeadline}
                </if>
                <if test="urgencyLevel != null and urgencyLevel != ''">
                    and t.urgency_level  = #{urgencyLevel}
                </if>
                <if test="engineeringCode != null and engineeringCode != ''">
                    and t.engineering_code  like concat('%', #{engineeringCode} ,'%')
                </if>
                <if test="orderInspectionNumber != null and orderInspectionNumber != ''">
                    and t.order_inspection_number  like concat('%', #{orderInspectionNumber} ,'%')
                </if>
                <if test="reportRequirements != null and reportRequirements != ''">
                    and t.report_requirements  = #{reportRequirements}
                </if>
                <if test="reportFormat != null and reportFormat != ''">
                    and t.report_format REGEXP #{reportFormat}
                </if>
                <if test="dataFormat != null and dataFormat != ''">
                    and t.data_format REGEXP #{dataFormat}
                </if>
                <if test="dataReqERep != null and dataReqERep != ''">
                    and t.data_req_e_rep  = #{dataReqERep}
                </if>
                <if test="dataReqsPapereport != null and dataReqsPapereport != ''">
                    and t.data_reqs_papereport  = #{dataReqsPapereport}
                </if>
                <if test="otherRequirements != null and otherRequirements != ''">
                    and t.other_requirements  = #{otherRequirements}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="uploadAttachment != null and uploadAttachment != ''">
                    and t.upload_attachment  = #{uploadAttachment}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="productionStage != null and productionStage != ''">
                    and t.production_stage  = #{productionStage}
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
                <if test="acceptanceNotice != null and acceptanceNotice != ''">
                    and t.acceptance_notice  = #{acceptanceNotice}
                </if>
                <if test="purchaseOrderContractNumber != null and purchaseOrderContractNumber != ''">
                    and t.purchase_order_contract_number  like concat('%', #{purchaseOrderContractNumber} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and p.product_name like concat('%', #{productName}, '%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and p.product_model like concat('%', #{productModel}, '%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and p.manufacturer like concat('%', #{manufacturer}, '%')
                </if>

                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
            GROUP BY t.id
            ORDER BY t.codex_torch_create_datetime DESC
	</select>
     <select id="selectOptionsByEntrustedUnit" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.entrusted_unit label,
        	t.id value
        from customer_information_management t
        WHERE t.customer_id0 != '' and t.codex_torch_deleted = '0'
     </select>
    <select id="selectOptionsByCustomerManager" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.user_name label,
        t.id value
        from sys_user t
        where t.group_id = '1943617310996697094'
    </select>
     <select id="selectDataLinkageByEntrustedUnit" parameterType="String"
             resultType="java.util.Map">
        select
            t.settlement_unit as settlementUnit,
            t.customer_manager as customerManager
        from customer_information_management t
        WHERE t.customer_id0 = #{customer_id0}
            and t.codex_torch_deleted = '0'
     </select>

    <select id="selectEvaluationOrderList" parameterType="com.huatek.frame.modules.business.service.dto.EvaluationOrderDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.EvaluationOrderVO">
        select
        <include refid="Base_Column_List" />,
        <include refid="Customer_Infomation_Managemant"/>,
        p.product_model as productModel,
        p.manufacturer as manufacturer,
        p.product_name as productName,
        p.test_type as testType,
        s.specification_number as standardSpecificationId
        from evaluation_order t
        LEFT JOIN customer_information_management c ON c.id = t.customer_id
        LEFT JOIN product_list p on p.evaluation_order_id = t.id
        LEFT JOIN standard_specification s on p.standard_specification_id = s.id
        <where>
            and t.codex_torch_deleted = '0'
            <if test="orderNumber != null and orderNumber != ''">
                and t.order_number  like concat('%', #{orderNumber} ,'%')
            </if>
            <if test="orderType != null and orderType != ''">
                and t.order_type REGEXP #{orderType}
            </if>
            <if test="entrustedUnitName != null and entrustedUnitName != ''">
                and c.entrusted_unit like concat('%', #{entrustedUnitName}, '%')
            </if>
            <if test="settlementUnit != null and settlementUnit != ''">
                and c.settlement_unit like concat('%', #{settlementUnit}, '%')
            </if>
            <if test="customerManager != null and customerManager != ''">
                and c.customer_manager like concat('%', #{customerManager}, '%')
            </if>
            <if test="productModel != null and productModel != ''">
                and p.product_model like concat('%', #{productModel}, '%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and p.manufacturer like concat('%', #{manufacturer}, '%')
            </if>
            <if test="productName != null and productName != ''">
                and p.product_name like concat('%', #{productName}, '%')
            </if>
            <if test="testType != null and testType != ''">
                and p.test_type like concat('%', #{testType}, '%')
            </if>
            <if test="principal != null and principal != ''">
                and t.principal  like concat('%', #{principal} ,'%')
            </if>
            <if test="principalsPhoneNumber != null and principalsPhoneNumber != ''">
                and t.principals_phone_number  like concat('%', #{principalsPhoneNumber} ,'%')
            </if>
            <if test="dateOfEntrustment != null">
                and t.date_of_entrustment  = #{dateOfEntrustment}
            </if>
            <if test="deadline != null">
                and t.deadline  = #{deadline}
            </if>
            <if test="urgencyLevel != null and urgencyLevel != ''">
                and t.urgency_level  = #{urgencyLevel}
            </if>
            <if test="engineeringCode != null and engineeringCode != ''">
                and t.engineering_code  like concat('%', #{engineeringCode} ,'%')
            </if>
            <if test="orderInspectionNumber != null and orderInspectionNumber != ''">
                and t.order_inspection_number  like concat('%', #{orderInspectionNumber} ,'%')
            </if>
            <if test="reportRequirements != null and reportRequirements != ''">
                and t.report_requirements  = #{reportRequirements}
            </if>
            <if test="reportFormat != null and reportFormat != ''">
                and t.report_format REGEXP #{reportFormat}
            </if>
            <if test="dataFormat != null and dataFormat != ''">
                and t.data_format REGEXP #{dataFormat}
            </if>
            <if test="dataReqERep != null and dataReqERep != ''">
                and t.data_req_e_rep  = #{dataReqERep}
            </if>
            <if test="dataReqsPapereport != null and dataReqsPapereport != ''">
                and t.data_reqs_papereport  = #{dataReqsPapereport}
            </if>
            <if test="otherRequirements != null and otherRequirements != ''">
                and t.other_requirements  = #{otherRequirements}
            </if>
            <if test="comment != null and comment != ''">
                and t.comment  = #{comment}
            </if>
            <if test="uploadAttachment != null and uploadAttachment != ''">
                and t.upload_attachment  = #{uploadAttachment}
            </if>
            <if test="status != null and status != ''">
                and t.status  = #{status}
            </if>
            <if test="productionStage != null and productionStage != ''">
                and t.production_stage  = #{productionStage}
            </if>
            <if test="testMethodology != null and testMethodology != ''">
                and t.test_methodology  = #{testMethodology}
            </if>
            <if test="acceptanceNotice != null and acceptanceNotice != ''">
                and t.acceptance_notice  = #{acceptanceNotice}
            </if>
            <if test="purchaseOrderContractNumber != null and purchaseOrderContractNumber != ''">
                and t.purchase_order_contract_number  like concat('%', #{purchaseOrderContractNumber} ,'%')
            </if>
            <if test="productName != null and productName != ''">
                and p.product_name like concat('%', #{productName}, '%')
            </if>
            <if test="productModel != null and productModel != ''">
                and p.product_model like concat('%', #{productModel}, '%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and p.manufacturer like concat('%', #{manufacturer}, '%')
            </if>

            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
        GROUP BY t.id
        ORDER BY t.codex_torch_create_datetime DESC
	</select>

    <select id="selectEvaluationOrderListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.EvaluationOrderVO">
		select
		<include refid="Base_Column_List" />
			from evaluation_order t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
    <select id="findEvaluationOrderdetail"
            resultType="com.huatek.frame.modules.business.domain.vo.EvaluationOrderVO">
        select
        <include refid="Base_Column_List"/>, c.entrusted_unit as entrustedUnitName,
        c.settlement_unit as settlementUnit,
        c.customer_manager as customerManager,
        u.user_name as creator
        FROM evaluation_order t
        LEFT JOIN customer_information_management c ON t.customer_id = c.id
        LEFT JOIN sys_user u ON u.id = t.codex_torch_creator_id
        where t.id = #{id}
    </select>

</mapper>