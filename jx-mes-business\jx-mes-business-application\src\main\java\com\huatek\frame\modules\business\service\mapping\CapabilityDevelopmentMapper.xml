<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.CapabilityDevelopmentMapper">
    <sql id="Base_Column_List">
        t.id as id,
        t.development_team as developmentTeam,
        t.capability_type as capabilityType,
        t.task_number as taskNumber,
        t.task_description as taskDescription,
        t.status as status,
        t.`source` as `source`,
        t.source_order_number as sourceOrderNumber,
        t.applicable_test_type as applicableTestType,
        t.entrusted_unit as entrustedUnit,
        t.product_model as productModel,
        t.product_name as productName,
        t.manufacturer as manufacturer,
        t.product_category as productCategory,
        t.estimated_completion_time as estimatedCompletionTime,
        t.ecpat as ecpat,
        t.estimated_fixture_arrival_time as estimatedFixtureArrivalTime,
        t.estd_start_dbg_time as estdStartDbgTime,
        t.number_of_boards as numberOfBoards,
        t.board_aging_capability as boardAgingCapability,
        t.applicable_equipment_type as applicableEquipmentType,
        t.debugging_equipment_number as debuggingEquipmentNumber,
        t.device_name as deviceName,
        t.number_of_completed_pc_bs as numberOfCompletedPcBs,
        t.pcb_number as pcbNumber,
        t.program_number as programNumber,
        t.program_version as programVersion,
        t.completed10 as completed10,
        t.`comment` as `comment`,
        t.product_information1 as productInformation1,
        t.operation_card as operationCard,
        t.capability_asset_number as capabilityAssetNumber,
        t.engineer as engineer,
        t.codex_torch_creator_id as codexTorchCreatorId,
        t.codex_torch_updater as codexTorchUpdater,
        t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_create_datetime as codexTorchCreateDatetime,
        t.codex_torch_update_datetime as codexTorchUpdateDatetime,
        t.codex_torch_deleted as codexTorchDeleted
    </sql>
    <sql id="Base_Column_List2">
        t.id as id,
        t.development_team as developmentTeam,
        t.capability_type as capabilityType,
        t.task_number as taskNumber,
        t.task_description as taskDescription,
        t.status as status,
        t.`source` as `source`,
        t.source_order_number as sourceOrderNumber,
        t.applicable_test_type as applicableTestType,
        t.entrusted_unit as entrustedUnit,
        t.product_model as productModel,
        t.product_name as productName,
        t.manufacturer as manufacturer,
        pc.category_name as productCategory,
        t.estimated_completion_time as estimatedCompletionTime,
        t.ecpat as ecpat,
        t.estimated_fixture_arrival_time as estimatedFixtureArrivalTime,
        t.estd_start_dbg_time as estdStartDbgTime,
        t.number_of_boards as numberOfBoards,
        t.board_aging_capability as boardAgingCapability,
        t.applicable_equipment_type as applicableEquipmentType,
        dt.device_type_name as deviceTypeName,
        t.debugging_equipment_number as debuggingEquipmentNumber,
        t.device_name as deviceName,
        t.number_of_completed_pc_bs as numberOfCompletedPcBs,
        t.pcb_number as pcbNumber,
        t.program_number as programNumber,
        t.program_version as programVersion,
        t.completed10 as completed10,
        t.`comment` as `comment`,
        t.product_information1 as productInformation1,
        t.operation_card as operationCard,
        t.capability_asset_number as capabilityAssetNumber,
        t.engineer as engineer,
        t.codex_torch_creator_id as codexTorchCreatorId,
        t.codex_torch_updater as codexTorchUpdater,
        t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_create_datetime as codexTorchCreateDatetime,
        t.codex_torch_update_datetime as codexTorchUpdateDatetime,
        t.codex_torch_deleted as codexTorchDeleted
    </sql>
    <select id="selectCapabilityDevelopmentPage" parameterType="com.huatek.frame.modules.business.service.dto.CapabilityDevelopmentDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.CapabilityDevelopmentVO">
        select
        <include refid="Base_Column_List2" />
        from capability_development t
        left join product_category pc on t.product_category = pc.id
        left join device_type dt on t.applicable_equipment_type = dt.device_type_code
        <where>
            and t.codex_torch_deleted = '0'
            <if test="developmentTeam != null and developmentTeam != ''">
                and t.development_team  = #{developmentTeam}
            </if>
            <if test="capabilityType != null and capabilityType != ''">
                and t.capability_type  = #{capabilityType}
            </if>
            <if test="taskNumber != null and taskNumber != ''">
                and t.task_number  like concat('%', #{taskNumber} ,'%')
            </if>
            <if test="taskDescription != null and taskDescription != ''">
                and t.task_description  like concat('%', #{taskDescription} ,'%')
            </if>
            <if test="status != null and status != ''">
                and t.status  = #{status}
            </if>
            <if test="source != null and source != ''">
                and t.source  = #{source}
            </if>
            <if test="sourceOrderNumber != null and sourceOrderNumber != ''">
                and t.source_order_number  like concat('%', #{sourceOrderNumber} ,'%')
            </if>
            <if test="applicableTestType != null and applicableTestType != ''">
                and t.applicable_test_type  = #{applicableTestType}
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="productModel != null and productModel != ''">
                and t.product_model  like concat('%', #{productModel} ,'%')
            </if>
            <if test="productName != null and productName != ''">
                and t.product_name  like concat('%', #{productName} ,'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and t.manufacturer  like concat('%', #{manufacturer} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pc.category_name  like concat('%', #{productCategory} ,'%')
            </if>
            <if test="estimatedCompletionTime != null">
                and t.estimated_completion_time  = #{estimatedCompletionTime}
            </if>
            <if test="ecpat != null">
                and t.ecpat  = #{ecpat}
            </if>
            <if test="estimatedFixtureArrivalTime != null">
                and t.estimated_fixture_arrival_time  = #{estimatedFixtureArrivalTime}
            </if>
            <if test="estdStartDbgTime != null">
                and t.estd_start_dbg_time  = #{estdStartDbgTime}
            </if>
            <if test="numberOfBoards != null and numberOfBoards != ''">
                and t.number_of_boards  = #{numberOfBoards}
            </if>
            <if test="boardAgingCapability != null and boardAgingCapability != ''">
                and t.board_aging_capability  = #{boardAgingCapability}
            </if>
            <if test="applicableEquipmentType != null and applicableEquipmentType != ''">
                and t.applicable_equipment_type  = #{applicableEquipmentType}
            </if>
            <if test="debuggingEquipmentNumber != null and debuggingEquipmentNumber != ''">
                and t.debugging_equipment_number  = #{debuggingEquipmentNumber}
            </if>
            <if test="deviceName != null and deviceName != ''">
                and t.device_name  like concat('%', #{deviceName} ,'%')
            </if>
            <if test="numberOfCompletedPcBs != null and numberOfCompletedPcBs != ''">
                and t.number_of_completed_pc_bs  like concat('%', #{numberOfCompletedPcBs} ,'%')
            </if>
            <if test="pcbNumber != null and pcbNumber != ''">
                and t.pcb_number  like concat('%', #{pcbNumber} ,'%')
            </if>
            <if test="programNumber != null and programNumber != ''">
                and t.program_number  like concat('%', #{programNumber} ,'%')
            </if>
            <if test="programVersion != null and programVersion != ''">
                and t.program_version  like concat('%', #{programVersion} ,'%')
            </if>
            <if test="completed10 != null and completed10 != ''">
                and t.completed10  = #{completed10}
            </if>
            <if test="comment != null and comment != ''">
                and t.comment  like concat('%', #{comment} ,'%')
            </if>
            <if test="productInformation1 != null and productInformation1 != ''">
                and t.product_information1  = #{productInformation1}
            </if>
            <if test="operationCard != null and operationCard != ''">
                and t.operation_card  = #{operationCard}
            </if>
            <if test="capabilityAssetNumber != null and capabilityAssetNumber != ''">
                and t.capability_asset_number  like concat('%', #{capabilityAssetNumber} ,'%')
            </if>
            <if test="engineer != null and engineer != ''">
                and t.engineer  like concat('%', #{engineer} ,'%')
            </if>
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
        order by  t.codex_torch_create_datetime desc
    </select>
    <select id="selectOptionsBySourceOrderNumber" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.work_order_number label,
        t.work_order_number value
        from production_order t
        WHERE t.work_order_number != ''
        and t.codex_torch_deleted = '0'
    </select>
    <select id="selectDataLinkageBySourceOrderNumber" parameterType="String"
            resultType="java.util.Map">
        select
        pl.product_model as productModel,
        pl.product_name as productName,
        pl.manufacturer as manufacturer,
        pl.product_category as productCategory,
        c.entrusted_unit as entrustedUnit
        from production_order t
        left join product_list pl on t.product=pl.id
        left join evaluation_order eo on t.order_number=eo.order_number
        left join customer_information_management c on eo.customer_id =c.id
        WHERE t.work_order_number = #{work_order_number}
        and t.codex_torch_deleted = '0'
        and pl.codex_torch_deleted = '0'
        and eo.codex_torch_deleted='0'
        and c.codex_torch_deleted='0'
    </select>
    <select id="selectOptionsByDebuggingEquipmentNumber" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.device_serial_number label,
        t.device_serial_number value
        from equipment_inventory t
        WHERE t.device_serial_number != ''
        and t.codex_torch_deleted = '0'
    </select>
    <select id="selectDataLinkageByDebuggingEquipmentNumber" parameterType="String"
            resultType="java.util.Map">
        select
        t.device_name as deviceName
        from equipment_inventory t
        WHERE t.device_serial_number = #{device_serial_number}
        and t.codex_torch_deleted = '0'
    </select>

    <select id="selectCapabilityDevelopmentList" parameterType="com.huatek.frame.modules.business.service.dto.CapabilityDevelopmentDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.CapabilityDevelopmentVO">
        select
        <include refid="Base_Column_List2" />
        from capability_development t
        left join product_category pc on t.product_category = pc.id
        left join device_type dt on t.applicable_equipment_type = dt.device_type_code
        <where>
            and t.codex_torch_deleted = '0'
            <if test="developmentTeam != null and developmentTeam != ''">
                and t.development_team  = #{developmentTeam}
            </if>
            <if test="capabilityType != null and capabilityType != ''">
                and t.capability_type  = #{capabilityType}
            </if>
            <if test="taskNumber != null and taskNumber != ''">
                and t.task_number  like concat('%', #{taskNumber} ,'%')
            </if>
            <if test="taskDescription != null and taskDescription != ''">
                and t.task_description  like concat('%', #{taskDescription} ,'%')
            </if>
            <if test="status != null and status != ''">
                and t.status  = #{status}
            </if>
            <if test="source != null and source != ''">
                and t.source  = #{source}
            </if>
            <if test="sourceOrderNumber != null and sourceOrderNumber != ''">
                and t.source_order_number  like concat('%', #{sourceOrderNumber} ,'%')
            </if>
            <if test="applicableTestType != null and applicableTestType != ''">
                and t.applicable_test_type  = #{applicableTestType}
            </if>
            <if test="entrustedUnit != null and entrustedUnit != ''">
                and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
            </if>
            <if test="productModel != null and productModel != ''">
                and t.product_model  like concat('%', #{productModel} ,'%')
            </if>
            <if test="productName != null and productName != ''">
                and t.product_name  like concat('%', #{productName} ,'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and t.manufacturer  like concat('%', #{manufacturer} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pc.category_name  like concat('%', #{productCategory} ,'%')
            </if>
            <if test="estimatedCompletionTime != null">
                and t.estimated_completion_time  = #{estimatedCompletionTime}
            </if>
            <if test="ecpat != null">
                and t.ecpat  = #{ecpat}
            </if>
            <if test="estimatedFixtureArrivalTime != null">
                and t.estimated_fixture_arrival_time  = #{estimatedFixtureArrivalTime}
            </if>
            <if test="estdStartDbgTime != null">
                and t.estd_start_dbg_time  = #{estdStartDbgTime}
            </if>
            <if test="numberOfBoards != null and numberOfBoards != ''">
                and t.number_of_boards  = #{numberOfBoards}
            </if>
            <if test="boardAgingCapability != null and boardAgingCapability != ''">
                and t.board_aging_capability  = #{boardAgingCapability}
            </if>
            <if test="applicableEquipmentType != null and applicableEquipmentType != ''">
                and t.applicable_equipment_type  = #{applicableEquipmentType}
            </if>
            <if test="debuggingEquipmentNumber != null and debuggingEquipmentNumber != ''">
                and t.debugging_equipment_number  = #{debuggingEquipmentNumber}
            </if>
            <if test="deviceName != null and deviceName != ''">
                and t.device_name  like concat('%', #{deviceName} ,'%')
            </if>
            <if test="numberOfCompletedPcBs != null and numberOfCompletedPcBs != ''">
                and t.number_of_completed_pc_bs  like concat('%', #{numberOfCompletedPcBs} ,'%')
            </if>
            <if test="pcbNumber != null and pcbNumber != ''">
                and t.pcb_number  like concat('%', #{pcbNumber} ,'%')
            </if>
            <if test="programNumber != null and programNumber != ''">
                and t.program_number  like concat('%', #{programNumber} ,'%')
            </if>
            <if test="programVersion != null and programVersion != ''">
                and t.program_version  like concat('%', #{programVersion} ,'%')
            </if>
            <if test="completed10 != null and completed10 != ''">
                and t.completed10  = #{completed10}
            </if>
            <if test="comment != null and comment != ''">
                and t.comment  like concat('%', #{comment} ,'%')
            </if>
            <if test="productInformation1 != null and productInformation1 != ''">
                and t.product_information1  = #{productInformation1}
            </if>
            <if test="operationCard != null and operationCard != ''">
                and t.operation_card  = #{operationCard}
            </if>
            <if test="engineer != null and engineer != ''">
                and t.engineer  like concat('%', #{engineer} ,'%')
            </if>
            <if test="capabilityAssetNumber != null and capabilityAssetNumber != ''">
                and t.capability_asset_number  like concat('%', #{capabilityAssetNumber} ,'%')
            </if>
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectCapabilityDevelopmentListByIds"
            resultType="com.huatek.frame.modules.business.domain.vo.CapabilityDevelopmentVO">
        select
        <include refid="Base_Column_List" />
        from capability_development t
        <where>
            <if test="ids != null and ids.size > 0" >
                t.id in
                <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
                    #{id}                    </foreach>
            </if>
        </where>
    </select>
</mapper>