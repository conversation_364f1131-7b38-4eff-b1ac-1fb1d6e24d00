2025-09-03 14:41:27,033 ERROR gateway [reactor-http-nio-7] o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler [CompositeLog.java : 122] [16079dfa-746]  500 Server Error for HTTP PUT "/business/api/schedulePlan/issue"
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	|_ checkpoint ⇢ com.huatek.frame.gate.handler.CorsConfig$$Lambda$509/94500728 [DefaultWebFilterChain]
	|_ checkpoint ⇢ org.springframework.cloud.gateway.filter.WeightCalculatorWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ com.alibaba.csp.sentinel.adapter.spring.webflux.SentinelWebFluxFilter [DefaultWeb<PERSON><PERSON>er<PERSON>hain]
	|_ checkpoint ⇢ HTTP PUT "/business/api/schedulePlan/issue" [ExceptionHandlingWebHandler]
Stack trace:
		at sun.nio.ch.SocketDispatcher.read0(Native Method)
		at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
		at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
		at sun.nio.ch.IOUtil.read(IOUtil.java:192)
		at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
		at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
		at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
		at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:148)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:750)
