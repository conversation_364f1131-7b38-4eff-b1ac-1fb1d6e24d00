package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.annotation.datascope.DataScope;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.business.domain.ProductCategory;
import com.huatek.frame.modules.business.domain.ProductManagement;
import com.huatek.frame.modules.business.domain.vo.ProductManagementVO;
import com.huatek.frame.modules.business.mapper.ProductCategoryMapper;
import com.huatek.frame.modules.business.mapper.ProductManagementMapper;
import com.huatek.frame.modules.business.service.ProductManagementService;
import com.huatek.frame.modules.business.service.dto.ProductManagementDTO;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;


/**
 * 产品管理 ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "productManagement")
//@RefreshScope
@Slf4j
public class ProductManagementServiceImpl implements ProductManagementService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    private ProductManagementMapper productManagementMapper;

    @Autowired
    private ProductCategoryMapper productCategoryMapper;

    @Autowired
    protected Validator validator;

    private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    public ProductManagementServiceImpl() {

    }

    @Override
    //@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
    public TorchResponse<List<ProductManagementVO>> findProductManagementPage(ProductManagementDTO dto) {
        PageHelper.startPage(dto.getPage(), dto.getLimit());
        Page<ProductManagementVO> productManagements = productManagementMapper.selectProductManagementPage(dto);
        TorchResponse<List<ProductManagementVO>> response = new TorchResponse<List<ProductManagementVO>>();
        response.getData().setData(productManagements);
        response.setStatus(200);
        response.getData().setCount(productManagements.getTotal());
        return response;
    }


    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse<ProductManagement> saveOrUpdate(ProductManagementDTO productManagementDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(productManagementDto.getCodexTorchDeleted())) {
            productManagementDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = productManagementDto.getId();
        ProductManagement entity = new ProductManagement();
        BeanUtils.copyProperties(productManagementDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        if (HuatekTools.isEmpty(id)) {
            productManagementMapper.insert(entity);
        } else {
            productManagementMapper.updateById(entity);
        }

        TorchResponse<ProductManagement> response = new TorchResponse<>();
        response.getData().setData(entity);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    //@Cacheable(key = "#p0")
    public TorchResponse<ProductManagementVO> findProductManagement(String id) {
        ProductManagementVO vo = new ProductManagementVO();
        if (!HuatekTools.isEmpty(id)) {
            ProductManagement entity = productManagementMapper.selectById(id);
            if (HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
            }
            BeanUtils.copyProperties(entity, vo);
        }
        TorchResponse<ProductManagementVO> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(vo);
        return response;
    }

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse delete(String[] ids) {
        productManagementMapper.deleteBatchIds(Arrays.asList(ids));
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    public TorchResponse getOptionsList(String id) {
        if (selectOptionsFuncMap.size() == 0) {
            //初始化外键函数
            selectOptionsFuncMap.put("productCategory", productManagementMapper::selectOptionsByProductCategory);
        }

        //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
        PageHelper.startPage(1, 1000);
        Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

        TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
        response.getData().setData(selectOptionsVOs);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(selectOptionsVOs.getTotal());
        return response;
    }


    @Override
    @ExcelExportConversion(tableName = "product_management", convertorFields = "")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductManagementVO> selectProductManagementList(ProductManagementDTO dto) {
        return productManagementMapper.selectProductManagementList(dto);
    }

    /**
     * 导入产品管理数据
     *
     * @param productManagementList 产品管理数据列表
     * @param unionColumns          作为确认数据唯一性的字段集合
     * @param isUpdateSupport       是否更新支持，如果已存在，则进行更新数据
     * @param operName              操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "product_management", convertorFields = "")
    public TorchResponse importProductManagement(List<ProductManagementVO> productManagementList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(productManagementList) || productManagementList.size() == 0) {
            throw new ServiceException("导入产品管理数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProductManagementVO vo : productManagementList) {
            try {
                ProductManagement productManagement = new ProductManagement();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum++;
                    continue;
                }
                BeanUtils.copyProperties(vo, productManagement);
                QueryWrapper<ProductManagement> wrapper = new QueryWrapper();
                ProductManagement oldProductManagement = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn : unionColumns) {
                        try {
                            Field field = ProductManagementVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<ProductManagement> oldProductManagementList = productManagementMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldProductManagementList) && oldProductManagementList.size() > 1) {
                        productManagementMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldProductManagementList) && oldProductManagementList.size() == 1) {
                        oldProductManagement = oldProductManagementList.get(0);
                    }
                }
                if (StringUtils.isNull(oldProductManagement)) {
                    BeanValidators.validateWithException(validator, vo);
                    productManagementMapper.insert(productManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、产品名称 " + vo.getProductName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldProductManagement, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    productManagementMapper.updateById(oldProductManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、产品名称 " + vo.getProductName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、产品名称 " + vo.getProductName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、产品名称 " + vo.getProductName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(ProductManagementVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (!HuatekTools.isEmpty(vo.getProductCategory())) {
            List<String> productCategoryList = Arrays.asList(vo.getProductCategory().split(","));
            List<ProductCategory> list = productCategoryMapper.selectList(new QueryWrapper<ProductCategory>().in("category_name", productCategoryList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("产品分类=" + vo.getProductCategory() + "; ");
            }
        }
        if (failureRecord > 0) {
            failureNum++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProductManagementListByIds(List<String> ids) {
        List<ProductManagementVO> productManagementList = productManagementMapper.selectProductManagementListByIds(ids);

        TorchResponse<List<ProductManagementVO>> response = new TorchResponse<List<ProductManagementVO>>();
        response.getData().setData(productManagementList);
        response.setStatus(200);
        response.getData().setCount((long) productManagementList.size());
        return response;
    }

    @Override
    public TorchResponse<List<ProductManagementVO>> getProductManagementListByCategoryId(String categoryId) {
        List<ProductManagementVO> productManagementVOS = productManagementMapper.getProductManagementListByCategoryId(categoryId);

        TorchResponse<List<ProductManagementVO>> response = new TorchResponse<>();
        response.getData().setData(productManagementVOS);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }


}
