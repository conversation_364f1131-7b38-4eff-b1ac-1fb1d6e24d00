package com.huatek.frame.modules.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huatek.frame.modules.business.domain.SawOrder;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.business.domain.vo.SawOrderVO;
import com.huatek.frame.common.response.TorchResponse;

import java.util.List;


/**
* @description 监制验收工单Service
* <AUTHOR>
* @date 2025-08-20
**/
public interface SawOrderService extends IService<SawOrder> {
    
    /**
	 * 分页查找查找 监制验收工单
	 * 
	 * @param dto 监制验收工单dto实体对象
	 * @return 
	 */
	TorchResponse<List<SawOrderVO>> findSawOrderPage(SawOrderPageDTO requestParam);

    /**
	 * 添加 \修改 监制验收工单
	 * 
	 * @param requestParam 监制验收工单dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse<SawOrderVO> saveOrUpdate(SawOrderAddOrUpdateDTO requestParam);
	
	/**
	 * 通过id查找监制验收工单
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<SawOrderVO> findSawOrder(String id);
	
	/**
	 * 删除 监制验收工单
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(List<String> ids);

	/**
	 * 查找关联信息 监制验收工单
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<SawOrderVO>> getOptionsList(String id);




    /**
     * 根据条件查询监制验收工单列表
     *
     * @param dto 监制验收工单信息
     * @return 监制验收工单集合信息
     */
    List<SawOrderVO> selectSawOrderList(SawOrderDTO dto);

    /**
     * 导入监制验收工单数据
     *
     * @param sawOrderList 监制验收工单数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importSawOrder(List<SawOrderVO> sawOrderList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取监制验收工单数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectSawOrderListByIds(List<String> ids);

    /**
     * 监制验收工单主子表单组合提交
     *
	 * @param sawOrderDto 监制验收工单DTO实体对象
     * @return
     */
    public TorchResponse submitMasterDetails(SawOrderDTO sawOrderDto);


	/**
	 * 分页查询某个监制验收工单的产品明细
	 * @param id
	 * @return
	 */
	TorchResponse pageProduct(SawOrderProductPageDTO requestParam);

	/**
	 * 批量完成产品
	 * @param requestParam
	 * @return
	 */
	TorchResponse finishProduct(SawOrderProductFinishDTO requestParam);

	/**
	 * 批量完成监制验收工单
	 * @param requestParam
	 * @return
	 */
	TorchResponse finishSawOrder(SawOrderFinishDTO requestParam);
}