package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.modules.business.domain.StandardSpecificationInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import java.io.Serializable;
import java.util.List;

/**
* @description 产品列表VO实体类
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("产品列表DTO实体类")
public class ProductListVO implements Serializable {

	private static final Long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 测评订单ID
     **/
    @ApiModelProperty("测评订单ID")
    private String evaluationOrderId;

    /**
     * 监制验收工单id
     */
    @ApiModelProperty("监制验收工单id")
    private String sawOrderId;

    /**
     * 产品管理id
     */
    @ApiModelProperty("产品管理ID")
    private String productId;

    /**
     * 委托单位
     */
    @ApiModelProperty("委托单位")
    private String entrustedUnit;

    /**
     * 测评订单编号
     */
    @ApiModelProperty("测评订单编号")
    private String orderNumber;

    /**
     * 标准/规范编号id
     */
//    @Excel(name = "标准规范编号",
//            cellType = Excel.ColumnType.STRING,
//            type = Excel.Type.ALL)
    @ApiModelProperty("标准/规范编号id")
    private String standardSpecificationId;


    /**
     * 序号
     **/
    @ApiModelProperty("序号")
    @Excel(name = "序号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer serialNumber;

    /**
     * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testType;

    /**
     * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String status;

    /**
     * 监制验收产品状态
     */
    @ApiModelProperty("监制验收产品状态")
    private String sawStatus;

    /**
     * 要求完成日期
     **/
    @ApiModelProperty("要求完成日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "要求完成日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    private Date deadline;

    /**
     * 产品型号
     */
    @Excel(name = "产品型号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 生产批次
     **/
    @ApiModelProperty("生产批次")
    @Excel(name = "生产批次",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productionBatch;

    /**
     * 数量
     **/
    @ApiModelProperty("数量")
    @Excel(name = "数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer quantity;

    /**
     * 生产厂家
     */
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String manufacturer;

    /**
     * 产品分类
     */
    @ApiModelProperty("产品分类")
    private String productCategory;


    /**
     * 质量等级
     **/
    @ApiModelProperty("质量等级")
    @Excel(name = "质量等级",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String qualityGrade;

    /**
     * 标准规范编号
     */
    @ApiModelProperty("标准/规范编号")
    @Excel(name = "标准/规范编号", cellType = Excel.ColumnType.STRING, type = Excel.Type.ALL)
    private String standardSpecificationNumber;

    /**
     * 标准规范附件
     */
    @ApiModelProperty("标准/规范附件")
    private List<String> standardSpecificationAttachment;

    /**
     * 标准规范完整信息
     */
    @ApiModelProperty("标准规范完整信息")
    private List<StandardSpecificationInfo> standardSpecificationInfo;

    /**
     * 任务等级
     **/
    @ApiModelProperty("任务等级")
    @Excel(name = "任务等级",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String taskLevel;


    /**
     * 样本总数
     **/
    @ApiModelProperty("样本总数")
    private Integer sampleTotalCount;


    /**
     * 试验项目
     **/
    @ApiModelProperty("试验项目")
    private String experimentProject;

    /**
     * 专项分析试验项目
     **/
    @ApiModelProperty("专项分析试验项目")
    private String specialAnalysisTestProject;

    /**
     * 组别类型
     **/
    @ApiModelProperty("组别类型")
    private String groupType;

    /**
     * 鉴定试验项目
     **/
    @ApiModelProperty("鉴定试验项目")
    private String inspectionTestProject;

    /**
     * 质量一致性试验项目
     **/
    @ApiModelProperty("质量一致性试验项目")
    private String qualityConsistencyTestItems;

    /**
     * DPA试验项目
     **/
    @ApiModelProperty("DPA试验项目")
    private String dpaTestProject;

    /**
     * 其他试验
     **/
    @ApiModelProperty("其他试验")
    private String otherTests;

    /**
     * 技术对接姓名
     **/
    @ApiModelProperty("技术对接人姓名")
    private String technicalLiaisonName;

    /**
     * 技术对接人电话
     **/
    @ApiModelProperty("技术对接人电话")
    private String technicalLiaisonPhoneNumber;

    /**
     * 工单送检编号
     **/
    @ApiModelProperty("工单送检编号")
    @Excel(name = "工单送检编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderInspectionNumber;

    /**
     * 退回/不可筛原因
     **/
    @ApiModelProperty("退回/不可筛原因")
    @Excel(name = "退回/不可筛原因",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String rejectUnscreenableReason;


    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
//    @Excel(name = "更新人",
//        cellType = Excel.ColumnType.STRING,
//        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}