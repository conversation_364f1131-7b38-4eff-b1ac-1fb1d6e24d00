package com.huatek.frame.modules.business.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductBatchUpdateReqDTO {
    /**
     * 产品信息id列表
     */
    @ApiModelProperty("产品信息id列表")
    private List<String> ids;

    /**
     * 修改字段
     */
    @ApiModelProperty("修改字段")
    private String fieldName;

    /**
     * 修改值
     */
    @ApiModelProperty("修改值")
    private String value;

    /**
     * 退回原因
     */
    @ApiModelProperty("退回/不可筛原因")
    private String rejectUnscreenableReason;
}
