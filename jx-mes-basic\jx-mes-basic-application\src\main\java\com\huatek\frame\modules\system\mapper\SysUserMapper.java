package com.huatek.frame.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.system.domain.SysUser;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.domain.vo.SysUserVO;
import com.huatek.frame.modules.system.service.dto.RoleDTO;
import com.huatek.frame.modules.system.service.dto.SysUserDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 用户mapper
 * <AUTHOR>
 *
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

	/**
	 * 用户分页
	 * @param dto
	 * @return
	 */
	Page<SysUser> selectUserPage(SysUserDTO dto);

	List<SysUser> selectUsersByRole(RoleDTO roleDTO);

	List<SysUserVO> selectUserList(SysUserDTO dto);

	List<SysUserVO> findUserByGroupId(@Param("groupId") String groupId);

	/**
	 * 查询所有用户
	 * @return
	 */
    List<SysUserVO> findAllUsers();

	/**
	 * 根据部门获取该部门下所有用户
	 * @param requestParam 部门ID
	 * @return
	 */
    List<SysUserVO> getUsersByDepart(@Param("groupId") String requestParam);

	/**
	 * 根据用户id获取部门信息
	 * @param userId
	 * @return
	 */
    SysGroupVO findDepartById(@Param("userId") String userId);

	List<String> selectUserIdsByRole(String roleCode);


	/**
	 * 根据用户id查询用户
	 * @param roleId
	 * @return
	 */
    List<String> selectUsersByRoleId(@Param(value = "roleId") String roleId);
}
