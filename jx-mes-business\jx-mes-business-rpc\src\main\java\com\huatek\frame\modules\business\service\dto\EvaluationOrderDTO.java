package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.business.domain.BaseEntity;
import com.huatek.frame.modules.business.service.dto.ProductListDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;
import java.util.List;

/**
* @description 测评订单DTO 实体类
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("测评订单DTO实体类")
public class EvaluationOrderDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 客户信息ID
     */
    @ApiModelProperty("客户信息ID")
    private String customerId;

    /**
     * 委托单位
     */
    @ApiModelProperty("委托单位")
    private String entrustedUnit;

    /**
     * 委托单位名称
     */
    @ApiModelProperty("委托单位名称")
    private String entrustedUnitName;

    /**
     * 结算单位
     */
    @ApiModelProperty("结算单位")
    private String settlementUnit;

    /**
     * 客户经理
     */
    @ApiModelProperty("客户经理")
    private String customerManager;

    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;
    
    /**
	 * 订单类型
     **/
    @ApiModelProperty("订单类型")
    private String orderType;

    /**
	 * 委托人
     **/
    @ApiModelProperty("委托人")
    private String principal;

    /**
	 * 委托人电话
     **/
    @ApiModelProperty("委托人电话")
    private String principalsPhoneNumber;

    /**
     * 试验类型
     */
    @ApiModelProperty("试验类型")
    private String testType;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String productModel;

    /**
     * 生产厂家
     */
    @ApiModelProperty("生产厂家")
    private String manufacturer;


    /**
	 * 委托日期
     **/
    @ApiModelProperty("委托日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfEntrustment;

    /**
     * 委托起始日期
     **/
    @ApiModelProperty("委托起始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDateOfEntrustment;

    /**
     * 委托截止日期
     **/
    @ApiModelProperty("委托截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDateOfEntrustment;



    /**
	 * 要求完成日期
     **/
    @ApiModelProperty("要求完成日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadline;

    /**
     * 要求完成日期起始日期
     **/
    @ApiModelProperty("要求完成日期起始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDateOfDeadline;

    /**
     * 要求完成日期截止日期
     **/
    @ApiModelProperty("要求完成日期截止日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDateOfDeadline;
    
    /**
	 * 紧急程度
     **/
    @ApiModelProperty("紧急程度")
    private String urgencyLevel;

    /**
	 * 工程代码
     **/
    @ApiModelProperty("工程代码")
    private String engineeringCode;
    
    /**
	 * 订单送检编号
     **/
    @ApiModelProperty("订单送检编号")
    private String orderInspectionNumber;

    /**
	 * 报告需求
     **/
    @ApiModelProperty("报告需求")
    private String reportRequirements;

    /**
	 * 报告形式
     **/
    @ApiModelProperty("报告形式")
    private String reportFormat;

    /**
	 * 数据形式
     **/
    @ApiModelProperty("数据形式")
    private String dataFormat;

    /**
	 * 电子版报告数据要求
     **/
    @ApiModelProperty("电子版报告数据要求")
    private String dataReqERep;

    /**
	 * 纸质版报告数据要求
     **/
    @ApiModelProperty("纸质版报告数据要求")
    private String dataReqsPapereport;

    /**
	 * 其他要求
     **/
    @ApiModelProperty("其他要求")
    private String otherRequirements;

    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;

    /**
	 * 上传附件
     **/
    @ApiModelProperty("上传附件")
    private String uploadAttachment;
    
    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    private String status;
    
    /**
	 * 生产阶段
     **/
    @ApiModelProperty("生产阶段")
    private String productionStage;

    /**
	 * 试验方式
     **/
    @ApiModelProperty("试验方式")
    private String testMethodology;

    /**
	 * 验收通知单
     **/
    @ApiModelProperty("验收通知单")
    private String acceptanceNotice;

    /**
	 * 订货合同号
     **/
    @ApiModelProperty("订货合同号")
    private String purchaseOrderContractNumber;

    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


    /**
     * 子表明细项
     */
    @ApiModelProperty("子表明细项")
    private List<ProductListDTO> detailFormItems;

	/**
	 * 主表页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;


    /**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;



}