<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.AccountingStandardDetailsMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.product_category as productCategory,
<!--		t.experiment_project as experimentProject,-->
		t.quantity as quantity,
		t.unit as unit,
		t.charging_method as chargingMethod,
		t.minimum_quantity_requirement as minimumQuantityRequirement,
		t.base_price as basePrice,
		t.charge_standard_number as chargeStandardNumber,
		t.cfbrq as cfbrq,
		t.codex_torch_master_form_id as codexTorchMasterFormId,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectAccountingStandardDetailsPage" parameterType="com.huatek.frame.modules.business.service.dto.AccountingStandardDetailsDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.AccountingStandardDetailsVO">
		select
		<include refid="Base_Column_List" />,s.process_name2 as experimentProject
			from accounting_standard_details t
        left join standard_process_management s on t.experiment_project  = s.id
        <where>
                and t.codex_torch_deleted = '0'
                <if test="productCategory != null and productCategory != ''">
                    and t.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="experimentProject != null and experimentProject != ''">
                    and t.experiment_project  = #{experimentProject}
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  = #{quantity}
                </if>
                <if test="unit != null and unit != ''">
                    and t.unit  like concat('%', #{unit} ,'%')
                </if>
                <if test="chargingMethod != null and chargingMethod != ''">
                    and t.charging_method  = #{chargingMethod}
                </if>
                <if test="minimumQuantityRequirement != null and minimumQuantityRequirement != ''">
                    and t.minimum_quantity_requirement  = #{minimumQuantityRequirement}
                </if>
                <if test="basePrice != null and basePrice != ''">
                    and t.base_price  = #{basePrice}
                </if>
                <if test="chargeStandardNumber != null and chargeStandardNumber != ''">
                    and t.charge_standard_number  like concat('%', #{chargeStandardNumber} ,'%')
                </if>
                <if test="cfbrq != null and cfbrq != ''">
                    and t.cfbrq  = #{cfbrq}
                </if>
                <if test="codexTorchMasterFormId != null and codexTorchMasterFormId != ''">
                    and t.codex_torch_master_form_id  like concat('%', #{codexTorchMasterFormId} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>
     <select id="selectOptionsByExperimentProject" parameterType="String"
        resultType="com.huatek.frame.modules.business.domain.vo.SelectOptionsVO">
        select
            t.step_number label,
        	t.id value
        from standard_process_management t
        WHERE t.step_number != ''
         and t.codex_torch_deleted = '0'
     </select>

    <select id="selectOptionsByProductCategory" parameterType="String"
            resultType="com.huatek.frame.modules.business.domain.vo.SelectOptionsVO">
        select t.category_name as label,
        t.category_name value
        from product_category t where t.category_name !='' and t.CODEX_TORCH_DELETED ='0'
    </select>

    <select id="selectAccountingStandardDetailsList" parameterType="com.huatek.frame.modules.business.service.dto.AccountingStandardDetailsDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.AccountingStandardDetailsVO">
		select
		<include refid="Base_Column_List" />,s.process_name2 as experimentProject
			from accounting_standard_details t
        left join standard_process_management s on t.experiment_project  = s.id
        <where>
                and t.codex_torch_deleted = '0'
                <if test="productCategory != null and productCategory != ''">
                    and t.product_category  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="experimentProject != null and experimentProject != ''">
                    and t.experiment_project  = #{experimentProject}
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  = #{quantity}
                </if>
                <if test="unit != null and unit != ''">
                    and t.unit  like concat('%', #{unit} ,'%')
                </if>
                <if test="chargingMethod != null and chargingMethod != ''">
                    and t.charging_method  = #{chargingMethod}
                </if>
                <if test="minimumQuantityRequirement != null and minimumQuantityRequirement != ''">
                    and t.minimum_quantity_requirement  = #{minimumQuantityRequirement}
                </if>
                <if test="basePrice != null and basePrice != ''">
                    and t.base_price  = #{basePrice}
                </if>
                <if test="chargeStandardNumber != null and chargeStandardNumber != ''">
                    and t.charge_standard_number  like concat('%', #{chargeStandardNumber} ,'%')
                </if>
                <if test="cfbrq != null and cfbrq != ''">
                    and t.cfbrq  = #{cfbrq}
                </if>
                <if test="codexTorchMasterFormId != null and codexTorchMasterFormId != ''">
                    and t.codex_torch_master_form_id  like concat('%', #{codexTorchMasterFormId} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectAccountingStandardDetailsListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.AccountingStandardDetailsVO">
		select
		<include refid="Base_Column_List" />,s.process_name2 as experimentProject
			from accounting_standard_details t
        left join standard_process_management s on t.experiment_project  = s.id
        <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>