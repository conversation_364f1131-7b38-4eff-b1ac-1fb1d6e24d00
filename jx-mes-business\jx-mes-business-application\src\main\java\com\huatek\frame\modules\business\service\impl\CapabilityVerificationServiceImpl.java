package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.sql.Wrapper;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.CapabilityAsset;
import com.huatek.frame.modules.business.domain.CustomerInformationManagement;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO;
import com.huatek.frame.modules.business.domain.vo.CustomerInformationManagementVO;
import com.huatek.frame.modules.business.mapper.CapabilityAssetMapper;
import com.huatek.frame.modules.business.mapper.CustomerInformationManagementMapper;
import com.huatek.frame.modules.business.service.*;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.CapabilityVerification;
import com.huatek.frame.modules.business.domain.vo.CapabilityVerificationVO;
import com.huatek.frame.modules.business.mapper.CapabilityVerificationMapper;

import org.springframework.util.CollectionUtils;



/**
 * 能力核验 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "capabilityVerification")
//@RefreshScope
@Slf4j
public class CapabilityVerificationServiceImpl implements CapabilityVerificationService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private CapabilityVerificationMapper capabilityVerificationMapper;

	@Autowired
    private CustomerInformationManagementMapper customerInformationManagementMapper;

    @Autowired
    private CapabilityVerificationService capabilityVerificationService;

    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    private CapabilityAssetService capabilityAssetService;

    @Autowired
    private CapabilityAssetMapper capabilityAssetMapper;

    @Autowired
    private CapabilityDevelopmentService capabilityDevelopmentService;

    @Autowired
    private CapabilityReviewService capabilityReviewService;

    @Autowired
    private CustomerInformationManagementService customerInformationManagementService;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public CapabilityVerificationServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<CapabilityVerificationVO>> findCapabilityVerificationPage(CapabilityVerificationDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<CapabilityVerificationVO> capabilityVerifications = capabilityVerificationMapper.selectCapabilityVerificationPage(dto);
		TorchResponse<List<CapabilityVerificationVO>> response = new TorchResponse<List<CapabilityVerificationVO>>();
		response.getData().setData(capabilityVerifications);
		response.setStatus(200);
		response.getData().setCount(capabilityVerifications.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(CapabilityVerificationDTO capabilityVerificationDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(capabilityVerificationDto.getCodexTorchDeleted())) {
            capabilityVerificationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = capabilityVerificationDto.getId();
		CapabilityVerification entity = new CapabilityVerification();
        BeanUtils.copyProperties(capabilityVerificationDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        entity.setCustomerInfomationId(capabilityVerificationDto.getEntrustedUnit());
		if (HuatekTools.isEmpty(id)) {
            //todo 能力核验编号，如果只显示数字，可以去查数据库
            TorchResponse response = codeManagementService.getOrderNumber("NLHY");
            entity.setCapabilityVerificationNumber(response.getData().getData().toString());
            capabilityVerificationMapper.insert(entity);
		} else {
			capabilityVerificationMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        CapabilityVerificationVO vo = new CapabilityVerificationVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<CapabilityVerificationVO> findCapabilityVerification(String id) {
		CapabilityVerificationVO vo = new CapabilityVerificationVO();
		if (!HuatekTools.isEmpty(id)) {
			CapabilityVerification entity = capabilityVerificationMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<CapabilityVerificationVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<CapabilityVerification> capabilityVerificationList = capabilityVerificationMapper.selectBatchIds(Arrays.asList(ids));
        capabilityVerificationList.forEach(item -> {
            if (!StrUtil.equals(item.getStatus(), DicConstant.SalesOrder.CAPABILITY_VERIFICATION_STATUS_DRAFT)
                && !StrUtil.equals(item.getStatus(), DicConstant.SalesOrder.CAPABILITY_VERIFICATION_STATUS_VERIFIED)){
                throw new ServiceException("只能删除草稿状态和已核验状态的能力核验信息");
            }
            item.setCodexTorchDeleted(Constant.DEFAULT_YES);
            capabilityVerificationMapper.updateById(item);
        });
		//capabilityVerificationMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("entrustedUnit",capabilityVerificationMapper::selectOptionsByEntrustedUnit);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "capability_verification", convertorFields = "confirmationOfCapability,status,verificationResult")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<CapabilityVerificationVO> selectCapabilityVerificationList(CapabilityVerificationDTO dto) {
        return capabilityVerificationMapper.selectCapabilityVerificationList(dto);
    }

    /**
     * 导入能力核验数据
     *
     * @param capabilityVerificationList 能力核验数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "capability_verification", convertorFields = "confirmationOfCapability,status,verificationResult")
    public TorchResponse importCapabilityVerification(List<CapabilityVerificationVO> capabilityVerificationList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(capabilityVerificationList) || capabilityVerificationList.size() == 0) {
            throw new ServiceException("导入能力核验数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CapabilityVerificationVO vo : capabilityVerificationList) {
            try {
                CapabilityVerification capabilityVerification = new CapabilityVerification();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, capabilityVerification);
                QueryWrapper<CapabilityVerification> wrapper = new QueryWrapper();
                CapabilityVerification oldCapabilityVerification = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = CapabilityVerificationVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<CapabilityVerification> oldCapabilityVerificationList = capabilityVerificationMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldCapabilityVerificationList) && oldCapabilityVerificationList.size() > 1) {
                        capabilityVerificationMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldCapabilityVerificationList) && oldCapabilityVerificationList.size() == 1) {
                        oldCapabilityVerification = oldCapabilityVerificationList.get(0);
                    }
                }
                if (StringUtils.isNull(oldCapabilityVerification)) {
                    BeanValidators.validateWithException(validator, vo);
                    capabilityVerificationMapper.insert(capabilityVerification);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、能力核验编号 " + vo.getCapabilityVerificationNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldCapabilityVerification, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    capabilityVerificationMapper.updateById(oldCapabilityVerification);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、能力核验编号 " + vo.getCapabilityVerificationNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、能力核验编号 " + vo.getCapabilityVerificationNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、能力核验编号 " + vo.getCapabilityVerificationNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(CapabilityVerificationVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (!HuatekTools.isEmpty(vo.getCustomerInfomationId())) {
            List<String> entrustedUnitList = Arrays.asList(vo.getCustomerInfomationId().split(","));
            List<CustomerInformationManagement> list = customerInformationManagementMapper.selectList(new QueryWrapper<CustomerInformationManagement>().in("entrusted_unit", entrustedUnitList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("委托单位=" + vo.getCustomerInfomationId() + "; ");
            }
        }
        if (HuatekTools.isEmpty(vo.getProductModel())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>产品型号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getManufacturer())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>生产厂家不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse<List<CapabilityVerificationVO>> selectCapabilityVerificationListByIds(List<String> ids) {
        List<CapabilityVerificationVO> capabilityVerificationList = capabilityVerificationMapper.selectCapabilityVerificationListByIds(ids);

		TorchResponse<List<CapabilityVerificationVO>> response = new TorchResponse<List<CapabilityVerificationVO>>();
		response.getData().setData(capabilityVerificationList);
		response.setStatus(200);
		response.getData().setCount((long)capabilityVerificationList.size());
		return response;
    }

    @Override
    public TorchResponse verifyCapability(List<CapabilityVerificationCheckDTO> requestParam) {
        //拿到请求能力资产的核验结果集
        List<CapabilityVerificationCheckDTO> results = capabilityAssetService.selectCapabilityAssetListByMultiParams(requestParam).getData().getData();
        List<String> ids = new ArrayList<>();
        requestParam.forEach(item -> {
            ids.add(item.getId());
        });
        List<CapabilityVerificationVO> capabilityVerificationVOList = capabilityVerificationService.selectCapabilityVerificationListByIds(ids).getData().getData();
        for(int i = 0 ; i < capabilityVerificationVOList.size() ; i++){
            CapabilityVerificationVO item = capabilityVerificationVOList.get(i);
            item.setVerificationResult(results.get(i).getVerificationResult());
        }
//        try {
//            capabilityVerificationMapper.batchUpdateCapabilityVerification(capabilityVerificationVOList);
//        }catch (Exception ex){
//            throw new ServiceException("批量更新核验结果失败");
//        }
        capabilityVerificationMapper.batchUpdateCapabilityVerification(capabilityVerificationVOList);
        TorchResponse response = new TorchResponse();
        return response;
    }

    @Override
    public TorchResponse submitCapabilityVerification(List<String> ids) {
        List<CapabilityVerificationVO> capabilityVerificationVOList = capabilityVerificationMapper.selectCapabilityVerificationListByIds(ids);
        capabilityVerificationVOList.forEach(item -> {
//            if (!StrUtil.equals(item.getStatus(), DicConstant.SalesOrder.CAPABILITY_VERIFICATION_STATUS_PENDING_CONFIRMATION)){
//                throw new ServiceException("只能对待确认状态下产品进行能力评审");
//            }
            CustomerInformationManagementVO customerInformationManagementVO = customerInformationManagementService.findCustomerInformationManagement(item.getCustomerInfomationId()).getData().getData();
            CapabilityReviewDTO capabilityReviewDTO = CapabilityReviewDTO.builder()
                    .type("0") //todo 使用咨询类型字典
                    .productName(item.getProductName())
                    .productModel(item.getProductModel())
                    .manufacturer(item.getManufacturer())
                    .capabilityVerificationId(item.getId())
                    .productInformation1(item.getProductInformation())
                    .inspectionType(item.getConfirmationOfCapability())
                    .entrustedUnit(customerInformationManagementVO.getEntrustedUnit())
                    .build();
            capabilityReviewService.saveOrUpdate(capabilityReviewDTO);
            //更改能力核验状态
            item.setStatus(DicConstant.SalesOrder.CAPABILITY_VERIFICATION_STATUS_PENDING_CONFIRMATION);
            CapabilityVerification capabilityVerification = BeanUtil.toBean(item, CapabilityVerification.class);
            LambdaUpdateWrapper<CapabilityVerification> updateWrapper = Wrappers.lambdaUpdate(capabilityVerification)
                    .set(CapabilityVerification::getStatus, DicConstant.SalesOrder.CAPABILITY_VERIFICATION_STATUS_PENDING_CONFIRMATION);
            capabilityVerificationMapper.update(capabilityVerification, updateWrapper);
        });
        TorchResponse response = new TorchResponse();
        return response;

    }

    @Override
    public TorchResponse moveToDevCapabilityVerification(List<String> ids) {
        List<CapabilityVerificationVO> capabilityVerificationVOList = capabilityVerificationMapper.selectCapabilityVerificationListByIds(ids);

        capabilityVerificationVOList.forEach(item -> {
            //更新能力核验状态为转开发
            item.setStatus(DicConstant.SalesOrder.CAPABILITY_VERIFICATION_STATUS_TRANSFERRED_TO_DEV);
            item.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
            item.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserName());
            capabilityVerificationService.saveOrUpdate(BeanUtil.toBean(item, CapabilityVerificationDTO.class));
            CapabilityDevelopmentAddDTO capabilityDevelopmentDTO = CapabilityDevelopmentAddDTO.builder()
                    .source(DicConstant.TechnicalManagement.CAPABILITY_DEVELOPMENT_SOURCE_PROJECT)
                    .productModel(item.getProductModel())
                    .productName(item.getProductName())
                    .manufacturer(item.getManufacturer())
                    .entrustedUnit(item.getEntrustedUnit())
                    .productInformation1(item.getProductInformation())
                    .capabilityType(item.getConfirmationOfCapability())
                    .build();
            capabilityDevelopmentService.saveOrUpdateCapabilityDevelopment(capabilityDevelopmentDTO);

        });
        TorchResponse response = new TorchResponse();
        return response;
    }


}
