package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 能力开发VO实体类
* <AUTHOR>
* @date 2025-08-04
**/
@Data
@ApiModel("能力开发DTO实体类")
public class CapabilityDevelopmentVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 开发班组
     **/
    @ApiModelProperty("开发班组")
    @Excel(name = "开发班组",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String developmentTeam;

    /**
	 * 能力类型
     **/
    @ApiModelProperty("能力类型")
    @Excel(name = "能力类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String capabilityType;

    /**
	 * 任务编号
     **/
    @ApiModelProperty("任务编号")
    @Excel(name = "任务编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String taskNumber;

    /**
	 * 任务描述
     **/
    @ApiModelProperty("任务描述")
    @Excel(name = "任务描述",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String taskDescription;

    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String status;

    /**
	 * 来源
     **/
    @ApiModelProperty("来源")
    @Excel(name = "来源",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String source;

    /**
	 * 来源单号
     **/
    @ApiModelProperty("来源单号")
    @Excel(name = "来源单号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String sourceOrderNumber;

    /**
	 * 适用试验类型
     **/
    @ApiModelProperty("适用试验类型")
    @Excel(name = "适用试验类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String applicableTestType;

    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String entrustedUnit;

    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productName;

    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String manufacturer;

    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productCategory;

    /**
	 * 预计完成时间
     **/
    @ApiModelProperty("预计完成时间")
    @Excel(name = "预计完成时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date estimatedCompletionTime;

    /**
	 * 预计办卡到货时间
     **/
    @ApiModelProperty("预计办卡到货时间")
    @Excel(name = "预计办卡到货时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ecpat;

    /**
	 * 预计夹具到货时间
     **/
    @ApiModelProperty("预计夹具到货时间")
    @Excel(name = "预计夹具到货时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date estimatedFixtureArrivalTime;

    /**
	 * 预计开始调试时间
     **/
    @ApiModelProperty("预计开始调试时间")
    @Excel(name = "预计开始调试时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date estdStartDbgTime;

    /**
	 * 板子数量
     **/
    @ApiModelProperty("板子数量")
    @Excel(name = "板子数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long numberOfBoards;

    /**
	 * 每板老化能力
     **/
    @ApiModelProperty("每板老化能力")
    @Excel(name = "每板老化能力",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long boardAgingCapability;

    /**
	 * 适用设备类型
     **/
    @ApiModelProperty("适用设备类型code")
    private String applicableEquipmentType;
    /**
     * 适用设备类型
     **/
    @ApiModelProperty("适用设备类型名称")
    @Excel(name = "适用设备类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String deviceTypeName;



    /**
	 * 调试设备编号
     **/
    @ApiModelProperty("调试设备编号")
    @Excel(name = "调试设备编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String debuggingEquipmentNumber;

    /**
	 * 设备名称
     **/
    @ApiModelProperty("设备名称")
    @Excel(name = "设备名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String deviceName;

    /**
	 * 已完成板卡数量
     **/
    @ApiModelProperty("已完成板卡数量")
    @Excel(name = "已完成板卡数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long numberOfCompletedPcBs;

    /**
	 * 板卡编号
     **/
    @ApiModelProperty("板卡编号")
    @Excel(name = "板卡编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String pcbNumber;

    /**
	 * 程序编号
     **/
    @ApiModelProperty("程序编号")
    @Excel(name = "程序编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String programNumber;

    /**
	 * 程序版本
     **/
    @ApiModelProperty("程序版本")
    @Excel(name = "程序版本",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String programVersion;

    /**
	 * 完成
     **/
    @ApiModelProperty("完成")
    @Excel(name = "完成",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String completed10;

    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String comment;

    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    @Excel(name = "产品资料",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productInformation1;

    /**
	 * 操作卡
     **/
    @ApiModelProperty("操作卡")
    @Excel(name = "操作卡",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String operationCard;
    /**
     * 能力资产编号
     **/
    @ApiModelProperty("能力资产编号")
    @Excel(name = "能力资产编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String capabilityAssetNumber;

    /**
	 * 工程师
     **/
    @ApiModelProperty("工程师")
    @Excel(name = "工程师",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String engineer;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}