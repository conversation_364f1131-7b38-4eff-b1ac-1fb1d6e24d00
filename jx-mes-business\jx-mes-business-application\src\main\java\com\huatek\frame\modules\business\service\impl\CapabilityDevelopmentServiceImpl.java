package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.CapabilityDevelopment;
import com.huatek.frame.modules.business.domain.EquipmentInventory;
import com.huatek.frame.modules.business.domain.ProductionOrder;
import com.huatek.frame.modules.business.domain.vo.CapabilityDevelopmentVO;
import com.huatek.frame.modules.business.mapper.AwaitingProductionOrderMapper;
import com.huatek.frame.modules.business.mapper.CapabilityDevelopmentMapper;
import com.huatek.frame.modules.business.mapper.EquipmentInventoryMapper;
import com.huatek.frame.modules.business.service.CapabilityAssetService;
import com.huatek.frame.modules.business.service.CapabilityDevelopmentService;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.CollectionUtils;



/**
 * 能力开发 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-07
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "capabilityDevelopment")
//@RefreshScope
@Slf4j
public class CapabilityDevelopmentServiceImpl implements CapabilityDevelopmentService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    private CapabilityDevelopmentMapper capabilityDevelopmentMapper;

    @Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;
    @Autowired
    private EquipmentInventoryMapper equipmentInventoryMapper;

    @Autowired
    private CodeManagementService codeManagementService;
    @Autowired
    private CapabilityAssetService capabilityAssetService;

    @Autowired
    protected Validator validator;

    private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



    public CapabilityDevelopmentServiceImpl(){

    }

    @Override
    //@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
    public TorchResponse<List<CapabilityDevelopmentVO>> findCapabilityDevelopmentPage(CapabilityDevelopmentDTO dto) {
        PageHelper.startPage(dto.getPage(), dto.getLimit());
        Page<CapabilityDevelopmentVO> capabilityDevelopments = capabilityDevelopmentMapper.selectCapabilityDevelopmentPage(dto);
        TorchResponse<List<CapabilityDevelopmentVO>> response = new TorchResponse<List<CapabilityDevelopmentVO>>();
        response.getData().setData(capabilityDevelopments);
        response.setStatus(200);
        response.getData().setCount(capabilityDevelopments.getTotal());
        return response;
    }


    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveOrUpdate(CapabilityDevelopmentDTO capabilityDevelopmentDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(capabilityDevelopmentDto.getCodexTorchDeleted())) {
            capabilityDevelopmentDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = capabilityDevelopmentDto.getId();
        CapabilityDevelopment entity = new CapabilityDevelopment();
        BeanUtils.copyProperties(capabilityDevelopmentDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        if (HuatekTools.isEmpty(id)) {
            capabilityDevelopmentMapper.insert(entity);
        } else {
            capabilityDevelopmentMapper.updateById(entity);
        }

        TorchResponse response = new TorchResponse();
        CapabilityDevelopmentVO vo = new CapabilityDevelopmentVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    //@Cacheable(key = "#p0")
    public TorchResponse<CapabilityDevelopmentVO> findCapabilityDevelopment(String id) {
        CapabilityDevelopmentVO vo = new CapabilityDevelopmentVO();
        if (!HuatekTools.isEmpty(id)) {
            CapabilityDevelopment entity = capabilityDevelopmentMapper.selectById(id);
            if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
            }
            BeanUtils.copyProperties(entity, vo);
        }
        TorchResponse<CapabilityDevelopmentVO> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(vo);
        return response;
    }

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse delete(String[] ids) {
        List<CapabilityDevelopment> capabilityDevelopmentList = capabilityDevelopmentMapper.selectBatchIds(Arrays.asList(ids));

        // 检查是否有记录已加入能力资产
        List<String> assetAddedIds = new ArrayList<>();
        for (CapabilityDevelopment capabilityDevelopment : capabilityDevelopmentList) {
            if (!HuatekTools.isEmpty(capabilityDevelopment.getCapabilityAssetNumber())) {
                assetAddedIds.add(capabilityDevelopment.getId());
            }
        }

        if (!assetAddedIds.isEmpty()) {
            TorchResponse response = new TorchResponse();
            response.setStatus(Constant.REQUEST_SUCCESS);
            response.setMessage("以下记录已加入能力资产，不能进行删除操作：" + String.join(",", assetAddedIds));
            return response;
        }

        for (CapabilityDevelopment capabilityDevelopment : capabilityDevelopmentList) {
            capabilityDevelopment.setCodexTorchDeleted(Constant.DEFAULT_YES);
            capabilityDevelopmentMapper.updateById(capabilityDevelopment);
        }
        //capabilityDevelopmentMapper.deleteBatchIds(Arrays.asList(ids));
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    public TorchResponse getOptionsList(String id){
        if(selectOptionsFuncMap.size() == 0){
            //初始化外键函数
            selectOptionsFuncMap.put("sourceOrderNumber",capabilityDevelopmentMapper::selectOptionsBySourceOrderNumber);
            //初始化外键函数
            selectOptionsFuncMap.put("debuggingEquipmentNumber",capabilityDevelopmentMapper::selectOptionsByDebuggingEquipmentNumber);
        }

        //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
        PageHelper.startPage(1, 1000);
        Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

        TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
        response.getData().setData(selectOptionsVOs);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(selectOptionsVOs.getTotal());
        return response;
    }




    @Override
    public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
        Map<String, String> data = new HashMap();
        try {
            switch (linkageDataTableName) {
                case "production_order":
                    data = selectDataLinkageBySourceOrderNumber(conditionalValue);
                    break;
                case "equipment_inventory":
                    data = selectDataLinkageByDebuggingEquipmentNumber(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
    }
    @Override
    public Map<String,String> selectDataLinkageBySourceOrderNumber(String work_order_number) {
        return capabilityDevelopmentMapper.selectDataLinkageBySourceOrderNumber(work_order_number);
    }
    @Override
    public Map<String,String> selectDataLinkageByDebuggingEquipmentNumber(String device_serial_number) {
        return capabilityDevelopmentMapper.selectDataLinkageByDebuggingEquipmentNumber(device_serial_number);
    }

    @Override
    @ExcelExportConversion(tableName = "capability_development", convertorFields = "developmentTeam,capability_asset_capabilityType#capabilityType,status,source,capability_asset_applicableTestType#applicableTestType,equipment_inventory_deviceCategory#applicableEquipmentType,completed10")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<CapabilityDevelopmentVO> selectCapabilityDevelopmentList(CapabilityDevelopmentDTO dto) {
        return capabilityDevelopmentMapper.selectCapabilityDevelopmentList(dto);
    }

    /**
     * 导入能力开发数据
     *
     * @param capabilityDevelopmentList 能力开发数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "capability_development", convertorFields = "developmentTeam,capabilityType,status,source,applicableTestType,completed10")
    public TorchResponse importCapabilityDevelopment(List<CapabilityDevelopmentVO> capabilityDevelopmentList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(capabilityDevelopmentList) || capabilityDevelopmentList.size() == 0) {
            throw new ServiceException("导入能力开发数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CapabilityDevelopmentVO vo : capabilityDevelopmentList) {
            try {
                CapabilityDevelopment capabilityDevelopment = new CapabilityDevelopment();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, capabilityDevelopment);
                QueryWrapper<CapabilityDevelopment> wrapper = new QueryWrapper();
                CapabilityDevelopment oldCapabilityDevelopment = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = CapabilityDevelopmentVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<CapabilityDevelopment> oldCapabilityDevelopmentList = capabilityDevelopmentMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldCapabilityDevelopmentList) && oldCapabilityDevelopmentList.size() > 1) {
                        capabilityDevelopmentMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldCapabilityDevelopmentList) && oldCapabilityDevelopmentList.size() == 1) {
                        oldCapabilityDevelopment = oldCapabilityDevelopmentList.get(0);
                    }
                }
                if (StringUtils.isNull(oldCapabilityDevelopment)) {
                    BeanValidators.validateWithException(validator, vo);
                    capabilityDevelopmentMapper.insert(capabilityDevelopment);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、任务编号 " + vo.getTaskNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldCapabilityDevelopment, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    capabilityDevelopmentMapper.updateById(oldCapabilityDevelopment);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、任务编号 " + vo.getTaskNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、任务编号 " + vo.getTaskNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、任务编号 " + vo.getTaskNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(CapabilityDevelopmentVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (!HuatekTools.isEmpty(vo.getSourceOrderNumber())) {
            List<String> sourceOrderNumberList = Arrays.asList(vo.getSourceOrderNumber().split(","));
            List<ProductionOrder> list = awaitingProductionOrderMapper.selectList(new QueryWrapper<ProductionOrder>().in("work_order_number", sourceOrderNumberList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("来源单号=" + vo.getSourceOrderNumber() + "; ");
            }
        }
        if (!HuatekTools.isEmpty(vo.getDebuggingEquipmentNumber())) {
            List<String> debuggingEquipmentNumberList = Arrays.asList(vo.getDebuggingEquipmentNumber().split(","));
            List<EquipmentInventory> list = equipmentInventoryMapper.selectList(new QueryWrapper<EquipmentInventory>().in("device_serial_number", debuggingEquipmentNumberList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("调试设备编号=" + vo.getDebuggingEquipmentNumber() + "; ");
            }
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectCapabilityDevelopmentListByIds(List<String> ids) {
        List<CapabilityDevelopmentVO> capabilityDevelopmentList = capabilityDevelopmentMapper.selectCapabilityDevelopmentListByIds(ids);

        TorchResponse<List<CapabilityDevelopmentVO>> response = new TorchResponse<List<CapabilityDevelopmentVO>>();
        response.getData().setData(capabilityDevelopmentList);
        response.setStatus(200);
        response.getData().setCount((long)capabilityDevelopmentList.size());
        return response;
    }




    private static String getStatusByCompleted(String completed) {
        Map<String, String> aToBMap = new HashMap<>();
        aToBMap.put(DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_COMPLETED_QUANBUWANCHENG, DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_STATUS_QUANBUWANCHENG); // a: 部分完成 → b: 部分完成
        aToBMap.put(DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_COMPLETED_YICHANGGUANBI, DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_STATUS_YICHANGGUANBI); // a: 部分完成 → b: 部分完成
        aToBMap.put(DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_COMPLETED_BUFENWANCHENG, DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_STATUS_BUFENWANCHENG); // a: 部分完成 → b: 部分完成
        String bValue = aToBMap.get(completed);
        return bValue;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse uploadOperationCard(CapabilityDevelopmentCardDTO cardDto) {
        CapabilityDevelopment entity = capabilityDevelopmentMapper.selectById(cardDto.getId());
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        if (entity == null) {
            response.setMessage("记录不存在");
            return response;
        }

        entity.setOperationCard(cardDto.getOperationCard());
        entity.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));

        capabilityDevelopmentMapper.updateById(entity);

        //response.setMessage("操作卡上传成功");
        return response;
    }


    @Override
    public TorchResponse finishCapabilityDevelopment(CapabilityDevelopmentFinishDTO finishDto) {
        CapabilityDevelopment entity = capabilityDevelopmentMapper.selectById(finishDto.getId());
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        if (entity == null) {
            response.setMessage("记录不存在");
            return response;
        }

        // 检查是否已加入能力资产
        if (!HuatekTools.isEmpty(entity.getCapabilityAssetNumber())) {
            response.setMessage("该记录已加入能力资产，不能进行完成操作");
            return response;
        }

        BeanUtils.copyProperties(finishDto, entity);

        String bValue = getStatusByCompleted(finishDto.getCompleted10());
        entity.setStatus(bValue);

        entity.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));

        capabilityDevelopmentMapper.updateById(entity);
        //response.setMessage("操作成功");
        return response;
    }



    @Override
    public TorchResponse dispatchCapabilityDevelopment(CapabilityDevelopmentDispatchDTO dispatchDto) {
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 校验传入的 ids 是否为空
        if (dispatchDto.getIds() == null || dispatchDto.getIds().stream().count() == 0) {
            response.setMessage("未传入有效的ID集合");
            return response;
        }

        int successCount = 0;
        int failCount = 0;
        List<String> failIds = new ArrayList<>();
        List<String> invalidStatusIds = new ArrayList<>(); // 新增：记录状态不符合的ID

        for (String id : dispatchDto.getIds()) {
            CapabilityDevelopment entity = capabilityDevelopmentMapper.selectById(id);
            if (entity == null) {
                failCount++;
                failIds.add(id);
                continue;
            }
            if(!DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_STATUS_JINXINGZHONG.equals(entity.getStatus()) &&
                    !DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_STATUS_WEIKAISHI.equals(entity.getStatus())){
                failCount++;
                invalidStatusIds.add(id); // 记录状态不符合的ID
                continue; // 跳过后续处理
            }

            entity.setStatus(DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_STATUS_JINXINGZHONG);
            entity.setDevelopmentTeam(dispatchDto.getDevelopmentTeam());
            entity.setEngineer(dispatchDto.getEngineer());
            entity.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
            entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));

            capabilityDevelopmentMapper.updateById(entity);
            successCount++;
        }

        StringBuilder message = new StringBuilder(String.format("操作完成：成功 %d 条，失败 %d 条",
                successCount, failCount));

        if (!failIds.isEmpty()) {
            message.append("，无效ID: ").append(String.join(",", failIds));
        }

        if (!invalidStatusIds.isEmpty()) {
            message.append("，状态不允许分派的ID: ").append(String.join(",", invalidStatusIds))
                    .append("（仅允许分派'未开始'或'进行中'状态的记录）");
        }
        response.setMessage(message.toString());
        return response;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveOrUpdateCapabilityDevelopment(CapabilityDevelopmentAddDTO addDto) {
        CapabilityDevelopment entity = new CapabilityDevelopment();
        BeanUtils.copyProperties(addDto, entity);

        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));

        String id = addDto.getId();
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        if (HuatekTools.isEmpty(id)) {

            entity.setStatus(DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_STATUS_WEIKAISHI);
            TorchResponse responseCode = codeManagementService.getOrderNumber(BusinessConstant.CAPABILITY_VERIFICATION_NLKF);
            entity.setTaskNumber(responseCode.getData().getData().toString());

            entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
            entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
            capabilityDevelopmentMapper.insert(entity);
        } else {
            CapabilityDevelopment entity2 = capabilityDevelopmentMapper.selectById(id);
            if(!DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_STATUS_JINXINGZHONG.equals(entity2.getStatus()) &&
                    !DicConstant.TechnicalManagement.CAPABILITY_VERIFICATION_STATUS_WEIKAISHI.equals(entity2.getStatus())){
                response.setMessage("仅允许编辑'未开始'或'进行中'状态的记录");
                return response;
            }
            entity.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
            entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
            capabilityDevelopmentMapper.updateById(entity);
        }

        CapabilityDevelopmentVO vo = new CapabilityDevelopmentVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
        return response;
    }

    @Override
    public TorchResponse addCapabilityAsset(String[] ids) {
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);

        List<String> failedIds = new ArrayList<>();   // 查无此ID
        List<String> skippedIds = new ArrayList<>();  // 已生成过资产，跳过
        List<String> successIds = new ArrayList<>();  // 成功生成的ID

        for (String id : ids) {
            try {
                CapabilityDevelopment entity = capabilityDevelopmentMapper.selectById(id);

                if (entity == null) {
                    failedIds.add(id);
                    continue;
                }

                if (!HuatekTools.isEmpty(entity.getCapabilityAssetNumber())) {
                    skippedIds.add(id);
                    continue;
                }

                CapabilityAssetDTO capabilityAssetDTO = new CapabilityAssetDTO();
                BeanUtils.copyProperties(entity, capabilityAssetDTO);

                capabilityAssetDTO.setId("");

                TorchResponse responseCode = codeManagementService.getOrderNumber(BusinessConstant.CAPABILITY_VERIFICATION_NLZC);
                capabilityAssetDTO.setCapabilityNumber(responseCode.getData().getData().toString());

                if (entity.getBoardAgingCapability() != null && entity.getNumberOfCompletedPcBs() != null) {
                    capabilityAssetDTO.setTotalBoardAgingCapability(
                            entity.getBoardAgingCapability() * entity.getNumberOfCompletedPcBs()
                    );
                }

                capabilityAssetService.saveOrUpdate(capabilityAssetDTO);

                // 更新原表的能力资产编号
                entity.setCapabilityAssetNumber(capabilityAssetDTO.getCapabilityNumber());
                capabilityDevelopmentMapper.updateById(entity);

                successIds.add(id);
            } catch (Exception e) {
                failedIds.add(id); // 某条处理过程中异常，也跳过
                // 可选：记录日志 log.error("生成能力资产失败，ID: " + id, e);
            }
        }

        // 构建返回信息
        StringBuilder msg = new StringBuilder("加入能力资产完成。");
        if (!successIds.isEmpty()) {
            msg.append(" 成功: ").append(successIds.size()).append("项。");
        }
        if (!skippedIds.isEmpty()) {
            msg.append(" 跳过(已有资产): ").append(skippedIds.size()).append("项。");
        }
        if (!failedIds.isEmpty()) {
            msg.append(" 失败: ").append(failedIds.size()).append("项。");
            msg.append("失败ID: " + String.join(",", failedIds));
        }


        response.setMessage(msg.toString());
        return response;
    }

}
