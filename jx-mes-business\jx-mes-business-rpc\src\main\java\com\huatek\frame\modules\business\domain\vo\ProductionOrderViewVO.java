package com.huatek.frame.modules.business.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
* @description 待制工单VO实体类
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("工单查看VO")
public class ProductionOrderViewVO implements Serializable {

	private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderNumber;

    /**
     * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String orderNumber;

    /**
     * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String entrustedUnit;

    /**
     * 工程代码
     **/
    @ApiModelProperty("工程代码")
    @Excel(name = "工程代码",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String engineeringCode;

    /**
     * 订单备注
     **/
    @ApiModelProperty("订单备注")
    @Excel(name = "订单备注",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String orderRemarks;

    /**
     * 报告需求
     **/
    @ApiModelProperty("报告需求")
    @Excel(name = "报告需求",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String reportRequirements;

    /**
     * 报告形式
     **/
    @ApiModelProperty("报告形式")
    @Excel(name = "报告形式",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String reportFormat;

    /**
     * 电子版报告数据要求
     **/
    @ApiModelProperty("电子版报告数据要求")
    @Excel(name = "电子版报告数据要求",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String dataReqERep;

    /**
     * 纸质版报告数据要求
     **/
    @ApiModelProperty("纸质版报告数据要求")
    @Excel(name = "纸质版报告数据要求",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String dataReqsPapereport;

    /**
     * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productModel;

    /**
     * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productName;

    /**
     * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productCategory;

    /**
     * 生产批次
     **/
    @ApiModelProperty("生产批次")
    @Excel(name = "生产批次",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productionBatch;

    /**
     * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String manufacturer;

    /**
     * 送检数量
     **/
    @ApiModelProperty("送检数量")
    @Excel(name = "送检数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer inspectionQuantity;

    /**
     * 样本总数
     **/
    @ApiModelProperty("样本总数")
    @Excel(name = "样本总数",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Integer sampleTotalCount;

    /**
     * 任务等级
     **/
    @ApiModelProperty("任务等级")
    @Excel(name = "任务等级",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String taskLevel;

    /**
     * 标准规范号
     **/
    @ApiModelProperty("标准规范号")
    @Excel(name = "标准规范号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String standardSpecificationNumber;

    /**
     * 试验项目
     **/
    @ApiModelProperty("试验项目")
    @Excel(name = "试验项目",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String experimentProject;

    /**
     * 工单送检编号
     **/
    @ApiModelProperty("工单送检编号")
    @Excel(name = "工单送检编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderInspectionNumber;

    /**
     * 封装形式
     **/
    @ApiModelProperty("封装形式")
    @Excel(name = "封装形式",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String packageForm;

    /**
     * 质量等级
     **/
    @ApiModelProperty("质量等级")
    @Excel(name = "质量等级",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String qualityGrade;

    /**
     * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testType;

    /**
     * 要求完成日期
     **/
    @ApiModelProperty("要求完成日期")
    @Excel(name = "要求完成日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadline;

    /**
     * 试验方式
     **/
    @ApiModelProperty("试验方式")
    @Excel(name = "试验方式",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String testMethodology;

    /**
     * 前置工单
     **/
    @ApiModelProperty("前置工单")
    @Excel(name = "前置工单",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String predecessorWorkOrder;

    /**
     * 关联工单
     **/
    @ApiModelProperty("关联工单")
    @Excel(name = "关联工单",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String relatedWorkOrder;

    /**
     * PDA
     **/
    @ApiModelProperty("PDA")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "PDA",
            cellType = Excel.ColumnType.NUMERIC,
            type = Excel.Type.ALL)
    private BigDecimal pda;

    /**
     * 是否加入排产
     **/
    @ApiModelProperty("是否加入排产")
    @Excel(name = "是否加入排产",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String whetherToIncludeInScheduling;

    /**
     * 预计完成时间
     **/
    @ApiModelProperty("预计完成时间")
    @Excel(name = "预计完成时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date estimatedCompletionTime;

    /**
     * 数量
     **/
    @ApiModelProperty("数量")
    @Excel(name = "数量",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private Long quantity;

    /**
     * 附件
     **/
    @ApiModelProperty("附件")
    @Excel(name = "附件",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String attachment;
    /**
     * 报告
     **/
    @ApiModelProperty("报告")
    private String report;
    /**
     * 报告路径
     **/
    @ApiModelProperty("报告路径")
    private String reportPath;

    /**
     * 工单状态
     **/
    @ApiModelProperty("工单状态")
    @Excel(name = "工单状态",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderStatus;

    /**
     * 负责人
     **/
    @ApiModelProperty("负责人")
    @Excel(name = "负责人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String responsiblePerson;

    /**
     * 生产阶段
     **/
    @ApiModelProperty("生产阶段")
    @Excel(name = "生产阶段",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productionStage;

    /**
     * 是否同型同批二次检测
     **/
    @ApiModelProperty("是否同型同批二次检测")
    @Excel(name = "是否同型同批二次检测",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String wtstabr;
    /**
     * 所属部门名称
     **/
    @ApiModelProperty("所属部门名称")
    private String groupName;
    /**
     * 操作历史
     **/
    @ApiModelProperty("操作历史")
    List<ProductionOrderOperationHistoryVO> operatHistory;
    /**
     * 试验项目
     **/
    @ApiModelProperty("试验项目")
    List<ProductionTaskViewVO> experimentProjects;
}