package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 能力资产VO实体类
* <AUTHOR>
* @date 2025-08-04
**/
@Data
@ApiModel("能力资产DTO实体类")
public class CapabilityAssetVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 能力编号
     **/
    @ApiModelProperty("能力编号")
    @Excel(name = "能力编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String capabilityNumber;

    /**
	 * 能力类型
     **/
    @ApiModelProperty("能力类型")
    @Excel(name = "能力类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String capabilityType;

    /**
	 * 适用试验类型
     **/
    @ApiModelProperty("适用试验类型")
    @Excel(name = "适用试验类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String applicableTestType;

    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productName;

    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String manufacturer;

    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productCategory;

    /**
	 * 任务编号
     **/
    @ApiModelProperty("任务编号")
    @Excel(name = "任务编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String taskNumber;

    /**
	 * 适用设备类型
     **/
    @ApiModelProperty("适用设备类型")
    private String applicableEquipmentType;
    /**
     * 适用设备类型
     **/
    @ApiModelProperty("适用设备类型名称")
    @Excel(name = "适用设备类型",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String deviceTypeName;
    /**
	 * 程序编号
     **/
    @ApiModelProperty("程序编号")
    @Excel(name = "程序编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String programNumber;

    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    @Excel(name = "产品资料",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productInformation1;

    /**
	 * 每板老化能力
     **/
    @ApiModelProperty("每板老化能力")
    @Excel(name = "每板老化能力",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long boardAgingCapability;

    /**
	 * 老化板总能力
     **/
    @ApiModelProperty("老化板总能力")
    @Excel(name = "老化板总能力",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long totalBoardAgingCapability;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}