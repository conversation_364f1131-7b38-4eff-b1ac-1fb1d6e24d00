package com.huatek.frame.modules.business.service;

import com.huatek.frame.modules.business.domain.ProductManagement;
import com.huatek.frame.modules.business.service.dto.ProductManagementDTO;
import com.huatek.frame.modules.business.domain.vo.ProductManagementVO;
import com.huatek.frame.common.response.TorchResponse;
import java.util.List;


/**
* @description 产品管理Service
* <AUTHOR>
* @date 2025-07-14
**/
public interface ProductManagementService {
    
    /**
	 * 分页查找查找 产品管理
	 * 
	 * @param dto 产品管理dto实体对象
	 * @return 
	 */
	TorchResponse<List<ProductManagementVO>> findProductManagementPage(ProductManagementDTO dto);

    /**
	 * 添加 \修改 产品管理
	 * 
	 * @param productManagementDto 产品管理dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse<ProductManagement> saveOrUpdate(ProductManagementDTO productManagementDto);
	
	/**
	 * 通过id查找产品管理
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<ProductManagementVO> findProductManagement(String id);
	
	/**
	 * 删除 产品管理
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 产品管理
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<ProductManagementVO>> getOptionsList(String id);




    /**
     * 根据条件查询产品管理列表
     *
     * @param dto 产品管理信息
     * @return 产品管理集合信息
     */
    List<ProductManagementVO> selectProductManagementList(ProductManagementDTO dto);

    /**
     * 导入产品管理数据
     *
     * @param productManagementList 产品管理数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importProductManagement(List<ProductManagementVO> productManagementList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取产品管理数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectProductManagementListByIds(List<String> ids);


	/**
	 * 根据产品分类id查询产品集合
	 * @param categoryId 产品分类id
	 * @return
	 */
	TorchResponse<List<ProductManagementVO>> getProductManagementListByCategoryId(String categoryId);
}