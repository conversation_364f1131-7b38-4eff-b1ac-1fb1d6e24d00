package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.annotation.datascope.DataScope;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.bpm.constant.ApprovalStatus;
import com.huatek.frame.modules.bpm.constant.ProcessConstant;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.bpm.dto.ProcessFormDTO;
import com.huatek.frame.modules.bpm.service.ProcessInstanceService;
import com.huatek.frame.modules.bpm.service.ProcessPrivilegeService;
import com.huatek.frame.modules.business.domain.Outsourcing;
import com.huatek.frame.modules.business.domain.ProductionOrder;
import com.huatek.frame.modules.business.domain.vo.OutsourcingVO;
import com.huatek.frame.modules.business.service.ProductionTaskService;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import com.huatek.frame.modules.business.mapper.AwaitingProductionOrderMapper;
import com.huatek.frame.modules.business.mapper.OutsourcingMapper;
import com.huatek.frame.modules.business.service.AwaitingProductionOrderService;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.business.service.OutsourcingService;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.Dic;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.sql.Wrapper;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

//import static com.huatek.frame.modules.constant.DicConstant.ProductionOrder.OUTSOURCING_STATUS_DRAFT;


/**
 * 外协申请 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-07
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "outsourcingApplication")
//@RefreshScope
@Slf4j
public class OutsourcingServiceImpl extends ServiceImpl<OutsourcingMapper, Outsourcing> implements OutsourcingService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private OutsourcingMapper outsourcingMapper;

    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

    @Autowired
    private AwaitingProductionOrderService awaitingProductionOrderService;

    @Autowired
    private ProcessInstanceService processInstanceProxyService;

    @Autowired
    private ProcessPrivilegeService processPrivilegeService;

    @Autowired
    private ProductionTaskService productionTaskService;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();

    private String processBusinessKey = "外协申请";



	public OutsourcingServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<OutsourcingVO>> findOutsourcingApplicationPage(OutsourcingPageDTO requestParam) {

        String currentUser = SecurityContextHolder.getCurrentUserName();
        //Codex - 流程人员流程查看数据权限控制
        if(!processPrivilegeService.hasViewProcessPrivilege(requestParam.getId(),currentUser,processBusinessKey)){
            log.info("用户无权限查看流程, currentUser:{},processBusinessKey:{}",currentUser,processBusinessKey);
            TorchResponse<List<OutsourcingVO>> response = new TorchResponse<List<OutsourcingVO>>();
            response.getData().setData(new ArrayList<>());
            response.setStatus(200);
            response.getData().setCount(0L);
            return response;
        }
        //Codex - 流程人员流程查看数据权限控制
        if (currentUser.equals(ProcessConstant.SUPER_USER)){
            if(StringUtils.isEmpty(requestParam.getCodexTorchApplicant())){
                //管理员可查看所有数据
                requestParam.setCodexTorchApplicant("");
            }

            if(StringUtils.isEmpty(requestParam.getCodexTorchApprover())){
                requestParam.setCodexTorchApprover("");
            }
        }else if (requestParam.getWorkflowQueryRole().equals("applicant")){
            requestParam.setCodexTorchApplicant(currentUser);
        }else if(requestParam.getWorkflowQueryRole().equals("approver")){
            //多审批人处理
            requestParam.setCodexTorchApprover(null);
            requestParam.setCodexTorchApprovers(currentUser);

            if(StringUtils.isEmpty(requestParam.getCodexTorchApprovalStatus())) {
                requestParam.setCodexTorchApprovalStatus("待审批|已审批|已驳回");
            }else {
                requestParam.setCodexTorchApprovalStatus(requestParam.getCodexTorchApprovalStatus());
            }
        }
		PageHelper.startPage(requestParam.getPage(), requestParam.getLimit());
		Page<OutsourcingVO> outsourcingApplications = outsourcingMapper.selectOutsourcingApplicationPage(requestParam);
        //CodeX - 多级审批
        updateApproveStatusForCurrentUser(outsourcingApplications,currentUser);

		TorchResponse<List<OutsourcingVO>> response = new TorchResponse<List<OutsourcingVO>>();
		response.getData().setData(outsourcingApplications);
		response.setStatus(200);
		response.getData().setCount(outsourcingApplications.getTotal());
		return response;
	}

    /**
     * 多级审批，不同人看到的审批状态不同
     *
     * @param updateStatusVOList
     * @param currentUser
     */
    private void updateApproveStatusForCurrentUser(List<OutsourcingVO> updateStatusVOList,String currentUser){
        for(OutsourcingVO updateStatusVO : updateStatusVOList){
            String currentApprover = updateStatusVO.getCodexTorchApprover();
            String approvers = updateStatusVO.getCodexTorchApprovers();

            if(StringUtils.isEmpty(approvers)){
                continue;
            }

            //当前用户不在审批人之列，不能处理记录
            if(!approvers.contains(currentUser)){
                continue;
            }

            //当前用户是当前审批人,审批状态是待审批，不需要更新状态
            if(currentApprover.contains(currentUser)
                    && updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())){
                continue;
            }

            //如果当前用户处在当前审批者之前，更新审批状态为已审批
            if(!currentApprover.equals(currentUser) && (approvers.indexOf(currentApprover) > approvers.indexOf(currentUser))){
                if(updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
                    updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
                }
            }

            //如果当前用户在已审批用户中但不是最后一个审批用户，更新审批状态为已审批
            if(!currentApprover.equals(currentUser) && !approvers.endsWith(currentUser)) {
                if (approvers.contains(currentUser)) {
                    if (updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
                        updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
                    }
                }
            }
        }

    }


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(AddOrUpdateOutsourcingDTO requestParam) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(requestParam.getCodexTorchDeleted())) {
            requestParam.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = requestParam.getId();
        if(StringUtils.isEmpty(requestParam.getCodexTorchApplicant())){
            requestParam.setCodexTorchApplicant(currentUser);
        }else {
            if(!requestParam.getCodexTorchApplicant().equals(currentUser)){
                throw new ServiceException("表单保存失败，表单申请人不匹配，原申请人：" +  requestParam.getCodexTorchApplicant());
            }
        }
        requestParam.setCodexTorchApprovalStatus(ApprovalStatus.PENDING_SAVED.getName());
		Outsourcing entity = new Outsourcing();
        BeanUtils.copyProperties(requestParam, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
            TorchResponse response = codeManagementService.getOrderNumber("WXBM");
            entity.setOutsourcingNumber(response.getData().getData().toString());
            entity.setStatus(DicConstant.ProductionOrder.OUTSOURCING_STATUS_DRAFT);
			outsourcingMapper.insert(entity);
		} else {
            //已验收，更新工单/工序状态
            if (StrUtil.equals(entity.getStatus(), DicConstant.ProductionOrder.OUTSOURCING_STATUS_ACCEPTED)){
                if (StrUtil.equals(entity.getEntireOrProcess(), DicConstant.ProductionOrder.OUTSOURCING_TYPE_ENTIRE)){
                    ProductionOrderDTO productionOrderDTO = new ProductionOrderDTO();
                    productionOrderDTO.setId(entity.getOrderId());
                    productionOrderDTO.setOutSourcingStatus(requestParam.getStatus());
                    awaitingProductionOrderService.outSorucingBack(productionOrderDTO);
                }else {
                    productionTaskService.outsourcingAccept(entity.getProcessId());
                }
            }
			outsourcingMapper.updateById(entity);
		}
		TorchResponse response = new TorchResponse();
        OutsourcingVO vo = new OutsourcingVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<OutsourcingVO> findOutsourcingDetails(String id) {
		OutsourcingVO vo = new OutsourcingVO();
		if (!HuatekTools.isEmpty(id)) {
//			Outsourcing entity = outsourcingMapper.selectById(id);
            vo = outsourcingMapper.findOutsourcingDetails(id);
			if(HuatekTools.isEmpty(vo)) {
                throw new ServiceException("查询失败");
			}

		}
		TorchResponse<OutsourcingVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<Outsourcing> outsourcingApplicationList = outsourcingMapper.selectBatchIds(Arrays.asList(ids));
        for (Outsourcing outsourcingApplication : outsourcingApplicationList) {
            outsourcingApplication.setCodexTorchDeleted(Constant.DEFAULT_YES);
            outsourcingMapper.updateById(outsourcingApplication);
        }
		//outsourcingMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}

    @Override
    @Transactional
    public TorchResponse<Outsourcing> approve(FormApprovalDTO formApprovalDTO, String token) {
        log.info("表单审批,formApprovalDTO:{}",formApprovalDTO);
        String approvalAct = formApprovalDTO.getApprovalAct();
        ProcessFormDTO processFormDTO = new ProcessFormDTO();
        BeanUtils.copyProperties(formApprovalDTO,processFormDTO);
        processFormDTO.setBusinessKey(processBusinessKey);
        SysProcessRecordVO processRecordResp = processInstanceProxyService.approve(processFormDTO,token);

        Outsourcing updateProcessStatusEntity = outsourcingMapper.selectById(formApprovalDTO.getFormId());
//        if (StrUtil.equals(approvalAct, "AGREE")){
//            updateProcessStatusEntity.setStatus(DicConstant.ProductionOrder.OUTSOURCING_STATUS_APPROVALED);
//        }else if (StrUtil.equals(approvalAct, "REJECT")){
//            updateProcessStatusEntity.setStatus(DicConstant.ProductionOrder.OUTSOURCING_STATUS_REJECTED);
//        }

        updateProcessStatusEntity.setId(processRecordResp.getFormId());
        updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());
        updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());

        updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());
        outsourcingMapper.updateById(updateProcessStatusEntity);

        log.info("表单审批完成,processRecordResp:{}",processRecordResp);

        TorchResponse  response = new TorchResponse();
        response.getData().setData(updateProcessStatusEntity);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    public List<Outsourcing> batchApprove(List<FormApprovalDTO> formApprovals, String token){
        List<Outsourcing> outsourcings = new ArrayList<>();
        formApprovals.forEach(item -> {
            TorchResponse<Outsourcing> response = this.approve(item, token);
            outsourcings.add(response.getData().getData());
        });
        return outsourcings;
    }

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse apply(AddOrUpdateOutsourcingDTO outsourcingDto, String token) {
        log.info("表单提交申请,outsourcingApplicationDto:{}",outsourcingDto);

        JSONObject json = securityUser.currentUser(token);
        String currentUser = json.getString("userName");
        outsourcingDto.setCodexTorchApplicant(currentUser);
        outsourcingDto.setCodexTorchApprovalStatus("待审批");


        TorchResponse<OutsourcingVO> updateVoResp = this.saveOrUpdate(outsourcingDto);
        OutsourcingVO updateVo = updateVoResp.getData().getData();

        //CodeX 表单工作流绑定
        ProcessFormDTO processFormDTO = new ProcessFormDTO();
        processFormDTO.setProcessDefinitionKey("SimpleApprovalProcess");
        processFormDTO.setBusinessKey(processBusinessKey);
        processFormDTO.setFormId(updateVo.getId());
        SysProcessRecordVO processRecordResp = processInstanceProxyService.startProcessByKey(processFormDTO,token);
        Outsourcing updateProcessStatusEntity = new Outsourcing();
        //更新状态为待审批
        updateProcessStatusEntity.setStatus(DicConstant.ProductionOrder.OUTSOURCING_STATUS_PENDING_APPROVAL);
        updateProcessStatusEntity.setId(updateVo.getId());
        updateProcessStatusEntity.setCodexTorchApplicant(processRecordResp.getApplicant());
        updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());
        updateProcessStatusEntity.setStatus(DicConstant.ProductionOrder.OUTSOURCING_STATUS_PENDING_APPROVAL);

        //更新审批人列表
        updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());

        updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());
        outsourcingMapper.updateById(updateProcessStatusEntity);

        return updateVoResp;
    }






    @Override
    @ExcelExportConversion(tableName = "outsourcing_application", convertorFields = "status,processed")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<OutsourcingVO> selectOutsourcingApplicationList(OutsourcingDTO dto) {
        //Codex - 流程人员流程查看数据权限控制
        if (ProcessConstant.SUPER_USER.equals(dto.getCodexTorchApplicant())){
            //管理员可查看所有数据
            dto.setCodexTorchApplicant("");
            dto.setCodexTorchApprover("");
        }else if(StringUtils.isNotEmpty(dto.getCodexTorchApprover())){
            //多审批人处理
            dto.setCodexTorchApprovers(dto.getCodexTorchApprover());
            dto.setCodexTorchApprover(null);
        }
        return outsourcingMapper.selectOutsourcingApplicationList(dto);
    }

    /**
     * 导入外协申请数据
     *
     * @param outsourcingApplicationList 外协申请数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "outsourcing_application", convertorFields = "status,processed")
    public TorchResponse importOutsourcingApplication(List<OutsourcingVO> outsourcingApplicationList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(outsourcingApplicationList) || outsourcingApplicationList.size() == 0) {
            throw new ServiceException("导入外协申请数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (OutsourcingVO vo : outsourcingApplicationList) {
            try {
                Outsourcing outsourcingApplication = new Outsourcing();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, outsourcingApplication);
                QueryWrapper<Outsourcing> wrapper = new QueryWrapper();
                Outsourcing oldOutsourcingApplication = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = OutsourcingVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<Outsourcing> oldOutsourcingApplicationList = outsourcingMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldOutsourcingApplicationList) && oldOutsourcingApplicationList.size() > 1) {
                        outsourcingMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldOutsourcingApplicationList) && oldOutsourcingApplicationList.size() == 1) {
                        oldOutsourcingApplication = oldOutsourcingApplicationList.get(0);
                    }
                }
                if (StringUtils.isNull(oldOutsourcingApplication)) {
                    BeanValidators.validateWithException(validator, vo);
                    outsourcingMapper.insert(outsourcingApplication);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工单id " + vo.getOrderNumber() + " 导入成功");
                } else
                    if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldOutsourcingApplication, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    outsourcingMapper.updateById(oldOutsourcingApplication);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工单/工序id " + vo.getOrderNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、工单/工序id " + vo.getOrderNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、工单/工序id " + vo.getOrderNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(OutsourcingVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getOrderNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工单编号不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectOutsourcingApplicationListByIds(List<String> ids) {
        List<OutsourcingVO> outsourcingApplicationList = outsourcingMapper.selectOutsourcingApplicationListByIds(ids);

		TorchResponse<List<OutsourcingVO>> response = new TorchResponse<List<OutsourcingVO>>();
		response.getData().setData(outsourcingApplicationList);
		response.setStatus(200);
		response.getData().setCount((long)outsourcingApplicationList.size());
		return response;
    }

    @Override
    public TorchResponse batchUpdateOutSourcing(OutsourcingUpdateDTO requestParam, String token) {
        List<String> ids = requestParam.getIds();
        if (CollUtil.isEmpty(ids)){
            throw new ServiceException("请选择要审批的外协工单");
        }
        List<OutsourcingVO> outsourcingVOList = outsourcingMapper.selectOutsourcingApplicationListByIds(ids);

        List<FormApprovalDTO> formApprovals = ids.stream().map(id -> {
            FormApprovalDTO formApproval = new FormApprovalDTO();
            formApproval.setFormId(id);
            formApproval.setApprover(SecurityContextHolder.getCurrentUserName());
            formApproval.setApprovalOpinion(requestParam.getApprovalOpinion());
            formApproval.setApprovalAct(requestParam.getApprovalAct());
            return formApproval;
        }).collect(Collectors.toList());
        //批量进行审批
        List<Outsourcing> outsourcings = batchApprove(formApprovals, token);

        outsourcings.forEach(item -> {
            if (!StrUtil.equals(item.getStatus(), DicConstant.ProductionOrder.OUTSOURCING_STATUS_PENDING_APPROVAL)){
                throw new ServiceException("只能审批待审批状态下的工单");
            }
            if (StrUtil.equals(item.getCodexTorchApprovalStatus(), "待审批")){
                item.setStatus(DicConstant.ProductionOrder.OUTSOURCING_STATUS_PENDING_APPROVAL);
            }else if (StrUtil.equals(item.getCodexTorchApprovalStatus(), "已审批")){
                item.setStatus(DicConstant.ProductionOrder.OUTSOURCING_STATUS_APPROVALED);
            }else if (StrUtil.equals(item.getCodexTorchApprovalStatus(), "已驳回")){
                item.setStatus(DicConstant.ProductionOrder.OUTSOURCING_STATUS_REJECTED);
            }
            outsourcingMapper.updateById(item);
            //审批通过后，修改工单/工序状态
            if (StrUtil.equals(item.getStatus(), DicConstant.ProductionOrder.OUTSOURCING_STATUS_APPROVALED)){
                if (StrUtil.equals(item.getEntireOrProcess(), DicConstant.ProductionOrder.OUTSOURCING_TYPE_ENTIRE)){
                    //修改工单状态为已外协
                    ProductionOrderDTO productionOrderDTO = new ProductionOrderDTO();
                    productionOrderDTO.setId(item.getOrderId());
                    productionOrderDTO.setOutSourcingStatus(requestParam.getStatus());
                    awaitingProductionOrderService.outSorucingBack(productionOrderDTO);
                }else if (StrUtil.equals(item.getEntireOrProcess(), DicConstant.ProductionOrder.OUTSOURCING_TYPE_PROCESS)){
                    //修改工序状态为已外协
                    productionTaskService.outsourcingPass(item.getProcessId());
                }
            }

        });
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse findPendingOutsourcings(OutsourcingPageDTO requestParam) {
        PageHelper.startPage(requestParam.getPage(), requestParam.getLimit());
        Page<OutsourcingVO> outsourcings = outsourcingMapper.findPendingOutsourcings(requestParam);

        TorchResponse<List<OutsourcingVO>> response = new TorchResponse<>();
        response.getData().setData(outsourcings);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(outsourcings.getTotal());
        return response;



    }

    @Override
    public TorchResponse findOutsourcingHistorys(OutsourcingPageDTO requestParam) {
        PageHelper.startPage(requestParam.getPage(), requestParam.getLimit());

        Page<OutsourcingVO> outsourcingVOList = outsourcingMapper.findOutsourcingHistorys(requestParam);

        TorchResponse<List<OutsourcingVO>> response = new TorchResponse();
        response.getData().setData(outsourcingVOList);
        response.getData().setCount(outsourcingVOList.getTotal());
        return response;
    }

    @Override
    public TorchResponse batchApply(List<String> ids, String token) {
        List<OutsourcingVO> outsourcingVOList = outsourcingMapper.selectOutsourcingApplicationListByIds(ids);
        outsourcingVOList.forEach(item -> {
            AddOrUpdateOutsourcingDTO dto = BeanUtil.toBean(item, AddOrUpdateOutsourcingDTO.class);
            this.apply(dto , token);
        });

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;


    }


}
