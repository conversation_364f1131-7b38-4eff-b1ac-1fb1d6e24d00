package com.huatek.frame.modules.business.utils;

import com.huatek.frame.modules.business.domain.vo.ProductionOrderIDCardVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderExpiredCardVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderIDCardDPAVO;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.util.IOUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 标识卡Excel导出工具类
 * 支持三种类型的标识卡：非DPA标识卡、非DPA失效标识卡、DPA标识卡
 * 每张标识卡为7行2列格式，带边框，多个标识卡换行排列
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public class IDCardExcelExporter {

    private static final int CARD_ROWS = 7; // 每张标识卡7行
    private static final int CARD_COLS = 2; // 每张标识卡2列
    private static final int DEFAULT_CARDS_PER_ROW = 3; // 默认每行显示3张标识卡
    
    /**
     * 导出标识卡Excel（默认每行3张卡片）
     * @param response HTTP响应
     * @param cardDataList 标识卡数据列表
     * @throws IOException IO异常
     */
    public static void exportIDCards(HttpServletResponse response, List<Object> cardDataList) throws IOException {
        exportIDCards(response, cardDataList, DEFAULT_CARDS_PER_ROW);
    }

    /**
     * 导出标识卡Excel（可自定义每行卡片数量）
     * @param response HTTP响应
     * @param cardDataList 标识卡数据列表
     * @param cardsPerRow 每行显示的卡片数量（1=单列，2=双列，3=三列等）
     * @throws IOException IO异常
     */
    public static void exportIDCards(HttpServletResponse response, List<Object> cardDataList, int cardsPerRow) throws IOException {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("标识卡");
        
        // 设置列宽 - 左列窄一些，右列宽一些
        for (int i = 0; i < cardsPerRow * CARD_COLS; i++) {
            if (i % 2 == 0) {
                // 左列（标签列）：去掉三分之一宽度，从4000调整为2700
                sheet.setColumnWidth(i, 2500);
            } else {
                // 右列（数值列）：保持原宽度
                sheet.setColumnWidth(i, 7000);
            }
        }
        
        // 创建样式
        CellStyle headerLeftStyle = createHeaderLeftStyle(workbook);
        CellStyle headerRightStyle = createHeaderRightStyle(workbook);
        CellStyle labelStyle = createLabelStyle(workbook);
        CellStyle valueStyle = createValueStyle(workbook);
        CellStyle borderStyle = createBorderStyle(workbook);
        
        int currentRow = 0;
        int cardIndex = 0;
        
        while (cardIndex < cardDataList.size()) {
            // 计算当前行要显示的卡片数量
            int cardsInCurrentRow = Math.min(cardsPerRow, cardDataList.size() - cardIndex);
            boolean isfirstRow=true;
            // 为当前行的卡片创建行
            for (int rowInCard = 0; rowInCard < CARD_ROWS; rowInCard++) {
                Row row = sheet.createRow(currentRow + rowInCard);
                if (isfirstRow) {
                    isfirstRow=false;
                    row.setHeight((short) 600); // 设置行高
                } else {
                    row.setHeight((short) 320); // 设置行高
                }
                // 为每张卡片创建单元格
                for (int cardInRow = 0; cardInRow < cardsInCurrentRow; cardInRow++) {
                    Object cardData = cardDataList.get(cardIndex + cardInRow);
                    int startCol = cardInRow * CARD_COLS;
                    
                    createCardRow(row, startCol, rowInCard, cardData, headerLeftStyle, headerRightStyle, labelStyle, valueStyle, borderStyle);
                }
            }
            
            currentRow += CARD_ROWS + 1; // 卡片间留一行空隙
            cardIndex += cardsInCurrentRow;
        }
        
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "合格证_" + System.currentTimeMillis() + ".xlsx";
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));
        
        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }
    
    /**
     * 创建标识卡的一行
     */
    private static void createCardRow(Row row, int startCol, int rowInCard, Object cardData,
                                    CellStyle headerLeftStyle, CellStyle headerRightStyle, CellStyle labelStyle, CellStyle valueStyle, CellStyle borderStyle) {
        
        if (cardData instanceof ProductionOrderIDCardVO) {
            createNormalCardRow(row, startCol, rowInCard, (ProductionOrderIDCardVO) cardData, headerLeftStyle, headerRightStyle, labelStyle, valueStyle);
        } else if (cardData instanceof ProductionOrderExpiredCardVO) {
            createExpiredCardRow(row, startCol, rowInCard, (ProductionOrderExpiredCardVO) cardData, headerLeftStyle, headerRightStyle, labelStyle, valueStyle);
        } else if (cardData instanceof ProductionOrderIDCardDPAVO) {
            createDPACardRow(row, startCol, rowInCard, (ProductionOrderIDCardDPAVO) cardData, headerLeftStyle, headerRightStyle, labelStyle, valueStyle);
        }
    }
    
    /**
     * 创建非DPA标识卡行
     */
    private static void createNormalCardRow(Row row, int startCol, int rowInCard, ProductionOrderIDCardVO card,
                                          CellStyle headerLeftStyle, CellStyle headerRightStyle, CellStyle labelStyle, CellStyle valueStyle) {
        Cell leftCell = row.createCell(startCol);
        Cell rightCell = row.createCell(startCol + 1);

        switch (rowInCard) {
            case 0: // 标题行
                createHeaderCellWithLogo(leftCell, rightCell, headerLeftStyle, headerRightStyle, row.getSheet().getWorkbook());
                break;
            case 1: // 编号
                leftCell.setCellValue("编号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getWorkOrderNumber() != null ? card.getWorkOrderNumber() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 2: // 送筛单位
                leftCell.setCellValue("送筛单位");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getEntrustedUnit() != null ? card.getEntrustedUnit() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 3: // 器件型号
                leftCell.setCellValue("器件型号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductModel() != null ? card.getProductModel() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 4: // 生产批次
                leftCell.setCellValue("生产批次");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductionBatch() != null ? card.getProductionBatch() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 5: // 合格数
                leftCell.setCellValue("合格数");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getQualifiedQuantity() != null ? card.getQualifiedQuantity().toString() : "0");
                rightCell.setCellStyle(valueStyle);
                break;
            case 6: // 失效数
                leftCell.setCellValue("失效数");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getFailureQuantity() != null ? card.getFailureQuantity().toString() : "0");
                rightCell.setCellStyle(valueStyle);
                break;
        }
    }
    
    /**
     * 创建失效标识卡行
     */
    private static void createExpiredCardRow(Row row, int startCol, int rowInCard, ProductionOrderExpiredCardVO card,
                                           CellStyle headerLeftStyle, CellStyle headerRightStyle, CellStyle labelStyle, CellStyle valueStyle) {
        Cell leftCell = row.createCell(startCol);
        Cell rightCell = row.createCell(startCol + 1);

        switch (rowInCard) {
            case 0: // 标题行
                createHeaderCellWithLogo(leftCell, rightCell, headerLeftStyle, headerRightStyle, row.getSheet().getWorkbook());
                break;
            case 1: // 编号
                leftCell.setCellValue("编号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getWorkOrderNumber() != null ? card.getWorkOrderNumber() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 2: // 送筛单位
                leftCell.setCellValue("送筛单位");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getEntrustedUnit() != null ? card.getEntrustedUnit() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 3: // 器件型号
                leftCell.setCellValue("器件型号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductModel() != null ? card.getProductModel() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 4: // 生产批次
                leftCell.setCellValue("生产批次");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductionBatch() != null ? card.getProductionBatch() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 5: // 失效数
                leftCell.setCellValue("失效数");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getFailureQuantity() != null ? card.getFailureQuantity().toString() : "0");
                rightCell.setCellStyle(valueStyle);
                break;
            case 6: // 备注
                leftCell.setCellValue("备注");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getRemark() != null ? card.getRemark() : "");
                rightCell.setCellStyle(valueStyle);
                break;
        }
    }
    
    /**
     * 创建DPA标识卡行
     */
    private static void createDPACardRow(Row row, int startCol, int rowInCard, ProductionOrderIDCardDPAVO card,
                                       CellStyle headerLeftStyle, CellStyle headerRightStyle, CellStyle labelStyle, CellStyle valueStyle) {
        Cell leftCell = row.createCell(startCol);
        Cell rightCell = row.createCell(startCol + 1);

        switch (rowInCard) {
            case 0: // 标题行
                createHeaderCellWithLogo(leftCell, rightCell, headerLeftStyle, headerRightStyle, row.getSheet().getWorkbook());
                break;
            case 1: // 编号
                leftCell.setCellValue("编号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getWorkOrderNumber() != null ? card.getWorkOrderNumber() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 2: // 委托单位
                leftCell.setCellValue("委托单位");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getEntrustedUnit() != null ? card.getEntrustedUnit() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 3: // 委托类型
                leftCell.setCellValue("委托类型 DPA");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue("");
                rightCell.setCellStyle(valueStyle);
                break;
            case 4: // 器件型号
                leftCell.setCellValue("器件型号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductModel() != null ? card.getProductModel() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 5: // 生产批次
                leftCell.setCellValue("生产批次");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductionBatch() != null ? card.getProductionBatch() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 6: // 结论
                leftCell.setCellValue("结论");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getConclusion() != null ? card.getConclusion() : "");
                rightCell.setCellStyle(valueStyle);
                break;
        }
    }

    /**
     * 创建带Logo的标题单元格
     * @param leftCell 左侧单元格
     * @param rightCell 右侧单元格
     * @param headerLeftStyle 左侧标题样式
     * @param headerRightStyle 右侧标题样式
     * @param workbook 工作簿
     */
    private static void createHeaderCellWithLogo(Cell leftCell, Cell rightCell, CellStyle headerLeftStyle, CellStyle headerRightStyle, Workbook workbook) {
        try {
            // 右侧单元格设置"标识卡"文字（宋体14号加粗）
            rightCell.setCellValue("标 识 卡");
            rightCell.setCellStyle(headerRightStyle);

            // 左侧单元格设置文字（宋体9号，图片靠左文字靠右）
            leftCell.setCellValue("君信\n电子");
            leftCell.setCellStyle(headerLeftStyle);

            // 尝试加载并插入Logo图片
            insertLogoImage(leftCell, workbook);

        } catch (Exception e) {
            // 如果图片加载失败，使用文字作为备用
            leftCell.setCellValue("君信\n电子");
            leftCell.setCellStyle(headerLeftStyle);
        }
    }

    /**
     * 插入Logo图片到单元格
     * @param cell 目标单元格
     * @param workbook 工作簿
     */
    private static void insertLogoImage(Cell cell, Workbook workbook) {
        InputStream logoStream = null;
        try {
            // 首先尝试从文件系统加载
            try {
                logoStream = new FileInputStream(BusinessConstant.LOGOPATH);
            } catch (Exception e) {
                // 如果文件系统加载失败，尝试从classpath加载
                logoStream = IDCardExcelExporter.class.getClassLoader().getResourceAsStream("logo.png");
                if (logoStream == null) {
                    throw new RuntimeException("无法找到Logo图片文件");
                }
            }

            // 读取图片数据
            byte[] logoBytes = IOUtils.toByteArray(logoStream);

            // 添加图片到工作簿
            int pictureIdx = workbook.addPicture(logoBytes, Workbook.PICTURE_TYPE_PNG);

            // 获取绘图对象
            Drawing<?> drawing = cell.getSheet().createDrawingPatriarch();

            // 创建锚点（定位图片位置）
            CreationHelper helper = workbook.getCreationHelper();
            ClientAnchor anchor = helper.createClientAnchor();

            // 设置图片位置（在单元格内）
            anchor.setCol1(cell.getColumnIndex());
            anchor.setRow1(cell.getRowIndex());
            anchor.setCol2(cell.getColumnIndex() + 1);
            anchor.setRow2(cell.getRowIndex() + 1);

            // 设置图片在单元格内的偏移和大小
            // 图片往左往下移动，为右侧文字留出空间
            anchor.setDx1(50000);     // 左边距（更小，让图片更靠左）
            anchor.setDy1(50000);    // 上边距（增大，让图片往下移）
//            anchor.setDx2(-2200);  // 右边距（负值表示从右边界向左的距离，为文字留更多空间）
//            anchor.setDy2(-50);    // 下边距

            // 创建图片
            Picture picture = drawing.createPicture(anchor, pictureIdx);

            //等比例缩放
            picture.resize();

        } catch (Exception e) {
            // 图片加载失败时的处理已在上层方法中完成
            throw new RuntimeException("Logo图片加载失败: " + e.getMessage(), e);
        } finally {
            if (logoStream != null) {
                try {
                    logoStream.close();
                } catch (IOException e) {
                    // 忽略关闭流时的异常
                }
            }
        }
    }

    /**
     * 创建标题左侧样式（Logo + 文字）
     */
    private static CellStyle createHeaderLeftStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式：图片靠左，文字靠右
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体：宋体9号
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 9);
        font.setBold(true);
        style.setFont(font);

        // 设置自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建标题右侧样式（标识卡）
     */
    private static CellStyle createHeaderRightStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体：宋体14号加粗
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 14);
        font.setBold(true);
        style.setFont(font);

        // 设置自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建标签样式
     */
    private static CellStyle createLabelStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体：宋体9号
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 9);
        font.setBold(false);
        style.setFont(font);

        return style;
    }

    /**
     * 创建值样式
     */
    private static CellStyle createValueStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体：宋体9号
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 9);
        font.setBold(false);
        style.setFont(font);

        // 设置自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建边框样式
     */
    private static CellStyle createBorderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }
}
