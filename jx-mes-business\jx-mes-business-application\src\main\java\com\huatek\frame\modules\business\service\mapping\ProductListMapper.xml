<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProductListMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.evaluation_order_id as evaluationOrderId,
        t.saw_order_id as sawOrderId,
        t.standard_specification_id as standardSpecificationId,
        t.manufacturer as manufacturer,
        t.product_category as productCategory,
        t.product_name as productName,
		t.serial_number as serialNumber,
		t.product_model as productModel,
		t.production_batch as productionBatch,
		t.quantity as quantity,
		t.task_level as taskLevel,
		t.test_type as testType,
		t.work_order_inspection_number as workOrderInspectionNumber,
		t.quality_grade as qualityGrade,
		t.sample_total_count as sampleTotalCount,
		t.experiment_project as experimentProject,
		t.special_analysis_test_project as specialAnalysisTestProject,
		t.group_type as groupType,
		t.inspection_test_project as inspectionTestProject,
		t.quality_consistency_test_items as qualityConsistencyTestItems,
		t.dpa_test_project as dpaTestProject,
		t.other_tests as otherTests,
		t.technical_liaison_name as technicalLiaisonName,
		t.technical_liaison_phone_number as technicalLiaisonPhoneNumber,
		t.deadline as deadline,
		t.status as status,
        t.saw_status as sawStatus,
		t.reject_unscreenable_reason as rejectUnscreenableReason,
        t.comment as comment,
        t.complete_date as completeDate,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>

    <resultMap id="ProductListResultMap" type="com.huatek.frame.modules.business.domain.vo.ProductListVO">
        <!-- 1. 主表主键映射（MyBatis通过主键识别唯一记录，必须保留） -->
        <id column="id" property="id"/>

        <!-- 2. 主表基础字段映射（完全保留原有配置，无需修改） -->
        <result column="evaluationOrderId" property="evaluationOrderId"/>
        <result column="sawOrderId" property="sawOrderId"/>
        <result column="standardSpecificationId" property="standardSpecificationId"/>
        <result column="manufacturer" property="manufacturer"/>
        <result column="productCategory" property="productCategory"/>
        <result column="productName" property="productName"/>
        <result column="serialNumber" property="serialNumber"/>
        <result column="productModel" property="productModel"/>
        <result column="productionBatch" property="productionBatch"/>
        <result column="quantity" property="quantity"/>
        <result column="taskLevel" property="taskLevel"/>
        <result column="testType" property="testType"/>
        <result column="workOrderInspectionNumber" property="workOrderInspectionNumber"/>
        <result column="qualityGrade" property="qualityGrade"/>
        <result column="sampleTotalCount" property="sampleTotalCount"/>
        <result column="experimentProject" property="experimentProject"/>
        <result column="specialAnalysisTestProject" property="specialAnalysisTestProject"/>
        <result column="groupType" property="groupType"/>
        <result column="inspectionTestProject" property="inspectionTestProject"/>
        <result column="qualityConsistencyTestItems" property="qualityConsistencyTestItems"/>
        <result column="dpaTestProject" property="dpaTestProject"/>
        <result column="otherTests" property="otherTests"/>
        <result column="technicalLiaisonName" property="technicalLiaisonName"/>
        <result column="technicalLiaisonPhoneNumber" property="technicalLiaisonPhoneNumber"/>
        <result column="deadline" property="deadline"/>
        <result column="status" property="status"/>
        <result column="sawStatus" property="sawStatus"/>
        <result column="rejectUnscreenableReason" property="rejectUnscreenableReason"/>
        <result column="codexTorchCreatorId" property="codexTorchCreatorId"/>
        <result column="codexTorchUpdater" property="codexTorchUpdater"/>
        <result column="codexTorchGroupId" property="codexTorchGroupId"/>
        <result column="codexTorchCreateDatetime" property="codexTorchCreateDatetime"/>
        <result column="codexTorchUpdateDatetime" property="codexTorchUpdateDatetime"/>
        <result column="codexTorchDeleted" property="codexTorchDeleted"/>

        <!-- 3. 标准规范编号（主查询中子查询返回的聚合结果） -->
        <result column="standardSpecificationNumber" property="standardSpecificationNumber"/>

        <!-- 4. 附件URL列表：调用子查询获取，彻底避开主表字段干扰 -->
        <collection
                property="standardSpecificationAttachment"
        ofType="java.lang.String"
        select="selectAttachmentUrlsByProductId"
        column="id"
        columnPrefix="productId="/>

        <collection
                property="standardSpecificationInfo"
        ofType="com.huatek.frame.modules.business.domain.StandardSpecificationInfo"
        select="selectStandardSpecificationInfoByProductId"
        column="id"
        columnPrefix="productId="/>
    </resultMap>
    <update id="batchUpdateProductList">
        <foreach collection="list" item="item" separator=";">
            UPDATE product_list t
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.evaluationOrderId != null and item.evaluationOrderId != ''">
                    t.evaluation_order_id = #{item.evaluationOrderId},
                </if>
                <if test="item.standardSpecificationId != null and item.standardSpecificationId != ''">
                    t.standard_specification_id = #{item.standardSpecificationId},
                </if>
                <if test="item.productModel != null and item.productModel != ''">
                    t.product_model = #{item.productModel},
                </if>
                <if test="item.manufacturer != null and item.manufacturer != ''">
                    t.manufacturer = #{item.manufacturer},
                </if>
                <if test="item.productCategory != null and item.productCategory != ''">
                    t.product_category = #{item.productCategory},
                </if>
                <if test="item.productName != null and item.productName != ''">
                    t.product_name = #{item.productName},
                </if>
                <if test="item.serialNumber != null and item.serialNumber != ''">
                    t.serial_number = #{item.serialNumber},
                </if>
                <if test="item.productionBatch != null and item.productionBatch != ''">
                    t.production_batch = #{item.productionBatch},
                </if>
                <if test="item.quantity != null">
                    t.quantity = #{item.quantity},
                </if>
                <if test="item.taskLevel != null and item.taskLevel != ''">
                    t.task_level = #{item.taskLevel},
                </if>
                <if test="item.testType != null and item.testType != ''">
                    t.test_type = #{item.testType},
                </if>
                <if test="item.workOrderInspectionNumber != null and item.workOrderInspectionNumber != ''">
                    t.work_order_inspection_number = #{item.workOrderInspectionNumber},
                </if>
                <if test="item.qualityGrade != null and item.qualityGrade != ''">
                    t.quality_grade = #{item.qualityGrade},
                </if>
                <if test="item.sampleTotalCount != null">
                    t.sample_total_count = #{item.sampleTotalCount},
                </if>
                <if test="item.experimentProject != null and item.experimentProject != ''">
                    t.experiment_project = #{item.experimentProject},
                </if>
                <if test="item.specialAnalysisTestProject != null and item.specialAnalysisTestProject != ''">
                    t.special_analysis_test_project = #{item.specialAnalysisTestProject},
                </if>
                <if test="item.groupType != null and item.groupType != ''">
                    t.group_type = #{item.groupType},
                </if>
                <if test="item.inspectionTestProject != null and item.inspectionTestProject != ''">
                    t.inspection_test_project = #{item.inspectionTestProject},
                </if>
                <if test="item.qualityConsistencyTestItems != null and item.qualityConsistencyTestItems != ''">
                    t.quality_consistency_test_items = #{item.qualityConsistencyTestItems},
                </if>
                <if test="item.dpaTestProject != null and item.dpaTestProject != ''">
                    t.dpa_test_project = #{item.dpaTestProject},
                </if>
                <if test="item.otherTests != null and item.otherTests != ''">
                    t.other_tests = #{item.otherTests},
                </if>
                <if test="item.technicalLiaisonName != null and item.technicalLiaisonName != ''">
                    t.technical_liaison_name = #{item.technicalLiaisonName},
                </if>
                <if test="item.technicalLiaisonPhoneNumber != null and item.technicalLiaisonPhoneNumber != ''">
                    t.technical_liaison_phone_number = #{item.technicalLiaisonPhoneNumber},
                </if>
                <if test="item.deadline != null">
                    t.deadline = #{item.deadline},
                </if>
                <if test="item.status != null and item.status != ''">
                    t.status = #{item.status},
                </if>
                <if test="item.rejectUnscreenableReason != null and item.rejectUnscreenableReason != ''">
                    t.reject_unscreenable_reason = #{item.rejectUnscreenableReason},
                </if>
                <if test="item.codexTorchDeleted != null and item.codexTorchDeleted != ''">
                    t.codex_torch_deleted = #{item.codexTorchDeleted},
                </if>
            </trim>
            WHERE t.id = #{item.id}
        </foreach>
    </update>
<!--    <select id="selectProductListPage" parameterType="com.huatek.frame.modules.business.service.dto.ProductListDTO"-->
<!--		resultMap="ProductListResultMap">-->
<!--		select-->
<!--		<include refid="Base_Column_List" />,-->
<!--        GROUP_CONCAT(s.controlled_number) as standardSpecificationNumber,-->
<!--        s.attachment as attachment-->
<!--        from product_list t-->
<!--        LEFT JOIN standard_specification s ON-->
<!--        FIND_IN_SET(s.id, t.standard_specification_id) > 0-->
<!--        OR s.id = t.standard_specification_id-->
<!--            <where>-->
<!--                and t.codex_torch_deleted = '0'-->
<!--                <if test="evaluationOrderId != null and evaluationOrderId != ''">-->
<!--                    and t.evaluation_order_id = #{evaluationOrderId}-->
<!--                </if>-->
<!--                <if test="serialNumber != null and serialNumber != ''">-->
<!--                    and t.serial_number  = #{serialNumber}-->
<!--                </if>-->
<!--                <if test="productModel != null and productModel != ''">-->
<!--                    and t.product_model  like concat('%', #{productModel} ,'%')-->
<!--                </if>-->
<!--                <if test="productName != null and productName != ''">-->
<!--                    and t.product_name  like concat('%', #{productName} ,'%')-->
<!--                </if>-->
<!--                <if test="productionBatch != null and productionBatch != ''">-->
<!--                    and t.production_batch  like concat('%', #{productionBatch} ,'%')-->
<!--                </if>-->
<!--                <if test="manufacturer != null and manufacturer != ''">-->
<!--                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')-->
<!--                </if>-->
<!--                <if test="quantity != null and quantity != ''">-->
<!--                    and t.quantity  = #{quantity}-->
<!--                </if>-->

<!--                <if test="taskLevel != null and taskLevel != ''">-->
<!--                    and t.task_level  = #{taskLevel}-->
<!--                </if>-->
<!--                <if test="testType != null and testType != ''">-->
<!--                    and t.test_type  = #{testType}-->
<!--                </if>-->
<!--                <if test="workOrderInspectionNumber != null and workOrderInspectionNumber != ''">-->
<!--                    and t.work_order_inspection_number  like concat('%', #{workOrderInspectionNumber} ,'%')-->
<!--                </if>-->
<!--                <if test="qualityGrade != null and qualityGrade != ''">-->
<!--                    and t.quality_grade  like concat('%', #{qualityGrade} ,'%')-->
<!--                </if>-->
<!--                <if test="sampleTotalCount != null and sampleTotalCount != ''">-->
<!--                    and t.sample_total_count  = #{sampleTotalCount}-->
<!--                </if>-->
<!--                <if test="experimentProject != null and experimentProject != ''">-->
<!--                    and t.experiment_project  like concat('%', #{experimentProject} ,'%')-->
<!--                </if>-->
<!--                <if test="specialAnalysisTestProject != null and specialAnalysisTestProject != ''">-->
<!--                    and t.special_analysis_test_project REGEXP #{specialAnalysisTestProject}-->
<!--                </if>-->
<!--                <if test="groupType != null and groupType != ''">-->
<!--                    and t.group_type REGEXP #{groupType}-->
<!--                </if>-->
<!--                <if test="inspectionTestProject != null and inspectionTestProject != ''">-->
<!--                    and t.inspection_test_project REGEXP #{inspectionTestProject}-->
<!--                </if>-->
<!--                <if test="qualityConsistencyTestItems != null and qualityConsistencyTestItems != ''">-->
<!--                    and t.quality_consistency_test_items REGEXP #{qualityConsistencyTestItems}-->
<!--                </if>-->
<!--                <if test="dpaTestProject != null and dpaTestProject != ''">-->
<!--                    and t.dpa_test_project REGEXP #{dpaTestProject}-->
<!--                </if>-->
<!--                <if test="otherTests != null and otherTests != ''">-->
<!--                    and t.other_tests  like concat('%', #{otherTests} ,'%')-->
<!--                </if>-->
<!--                <if test="technicalLiaisonName != null and technicalLiaisonName != ''">-->
<!--                    and t.technical_liaison_name  like concat('%', #{technicalLiaisonName} ,'%')-->
<!--                </if>-->
<!--                <if test="technicalLiaisonPhoneNumber != null and technicalLiaisonPhoneNumber != ''">-->
<!--                    and t.technical_liaison_phone_number  like concat('%', #{technicalLiaisonPhoneNumber} ,'%')-->
<!--                </if>-->
<!--                <if test="deadline != null">-->
<!--                    and t.deadline  = #{deadline}-->
<!--                </if>-->
<!--                <if test="status != null and status != ''">-->
<!--                    and t.status  = #{status}-->
<!--                </if>-->
<!--                <if test="rejectUnscreenableReason != null and rejectUnscreenableReason != ''">-->
<!--                    and t.reject_unscreenable_reason  like concat('%', #{rejectUnscreenableReason} ,'%')-->
<!--                </if>-->

<!--                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">-->
<!--                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')-->
<!--                </if>-->
<!--                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">-->
<!--                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')-->
<!--                </if>-->
<!--                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">-->
<!--                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')-->
<!--                </if>-->
<!--                <if test="codexTorchCreateDatetime != null">-->
<!--                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}-->
<!--                </if>-->
<!--                <if test="codexTorchUpdateDatetime != null">-->
<!--                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}-->
<!--                </if>-->
<!--                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">-->
<!--                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')-->
<!--                </if>-->
<!--                ${params.dataScope}-->
<!--            </where>-->
<!--            GROUP BY t.id,  s.attachment-->
<!--            ORDER BY t.serial_number ASC-->
<!--	</select>-->

    <!-- 子查询：根据产品ID，查询关联的标准规范完整信息（受控编号+附件URL） -->
    <select id="selectStandardSpecificationInfoByProductId" resultType="com.huatek.frame.modules.business.domain.StandardSpecificationInfo">
        SELECT
        s.controlled_number AS standardSpecificationNumber,
        s.attachment AS attachment
        FROM
        standard_specification s
        WHERE
        FIND_IN_SET(s.id, (SELECT t.standard_specification_id FROM product_list t WHERE t.id = #{productId})) > 0
        OR s.id = (SELECT t.standard_specification_id FROM product_list t WHERE t.id = #{productId})
        AND s.controlled_number IS NOT NULL
        AND s.attachment IS NOT NULL
    </select>

    <!-- 子查询：根据产品ID（t.id）查询关联的所有附件URL -->
    <select id="selectAttachmentUrlsByProductId" resultType="java.lang.String">
        SELECT
        s.attachment
        FROM
        standard_specification s
        WHERE
        FIND_IN_SET(s.id, (SELECT t.standard_specification_id FROM product_list t WHERE t.id = #{productId})) > 0
        OR s.id = (SELECT t.standard_specification_id FROM product_list t WHERE t.id = #{productId})
        AND s.attachment IS NOT NULL
    </select>

    <select id="selectProductListPage"
            parameterType="com.huatek.frame.modules.business.service.dto.ProductListDTO"
            resultMap="ProductListResultMap">
        select
        <include refid="Base_Column_List" />,
        (SELECT GROUP_CONCAT(DISTINCT s.controlled_number)
        FROM standard_specification s
        WHERE FIND_IN_SET(s.id, t.standard_specification_id) > 0
        OR s.id = t.standard_specification_id) AS standardSpecificationNumber
        from product_list t
        <where>
            and t.codex_torch_deleted = '0'
            <if test="evaluationOrderId != null and evaluationOrderId != ''">
                and t.evaluation_order_id = #{evaluationOrderId}
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                and t.serial_number  = #{serialNumber}
            </if>
            <if test="productModel != null and productModel != ''">
                and t.product_model  like concat('%', #{productModel} ,'%')
            </if>
            <if test="productName != null and productName != ''">
                and t.product_name  like concat('%', #{productName} ,'%')
            </if>
            <if test="productionBatch != null and productionBatch != ''">
                and t.production_batch  like concat('%', #{productionBatch} ,'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and t.manufacturer  like concat('%', #{manufacturer} ,'%')
            </if>
            <if test="quantity != null and quantity != ''">
                and t.quantity  = #{quantity}
            </if>

            <if test="taskLevel != null and taskLevel != ''">
                and t.task_level  = #{taskLevel}
            </if>
            <if test="testType != null and testType != ''">
                and t.test_type  = #{testType}
            </if>
            <if test="standardSpecificationNumber != null and standardSpecificationNumber != ''">
                and exists (
                    SELECT 1 FROM standard_specification s
                    WHERE (FIND_IN_SET(s.id, t.standard_specification_id) > 0 OR s.id = t.standard_specification_id)
                    AND s.controlled_number like concat('%', #{standardSpecificationNumber}, '%')
                )
            </if>
            <if test="workOrderInspectionNumber != null and workOrderInspectionNumber != ''">
                and t.work_order_inspection_number  like concat('%', #{workOrderInspectionNumber} ,'%')
            </if>
            <if test="qualityGrade != null and qualityGrade != ''">
                and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
            </if>
            <if test="sampleTotalCount != null and sampleTotalCount != ''">
                and t.sample_total_count  = #{sampleTotalCount}
            </if>
            <if test="experimentProject != null and experimentProject != ''">
                and t.experiment_project  like concat('%', #{experimentProject} ,'%')
            </if>
            <if test="specialAnalysisTestProject != null and specialAnalysisTestProject != ''">
                and t.special_analysis_test_project REGEXP #{specialAnalysisTestProject}
            </if>
            <if test="groupType != null and groupType != ''">
                and t.group_type REGEXP #{groupType}
            </if>
            <if test="inspectionTestProject != null and inspectionTestProject != ''">
                and t.inspection_test_project REGEXP #{inspectionTestProject}
            </if>
            <if test="qualityConsistencyTestItems != null and qualityConsistencyTestItems != ''">
                and t.quality_consistency_test_items REGEXP #{qualityConsistencyTestItems}
            </if>
            <if test="dpaTestProject != null and dpaTestProject != ''">
                and t.dpa_test_project REGEXP #{dpaTestProject}
            </if>
            <if test="otherTests != null and otherTests != ''">
                and t.other_tests  like concat('%', #{otherTests} ,'%')
            </if>
            <if test="technicalLiaisonName != null and technicalLiaisonName != ''">
                and t.technical_liaison_name  like concat('%', #{technicalLiaisonName} ,'%')
            </if>
            <if test="technicalLiaisonPhoneNumber != null and technicalLiaisonPhoneNumber != ''">
                and t.technical_liaison_phone_number  like concat('%', #{technicalLiaisonPhoneNumber} ,'%')
            </if>
            <if test="deadline != null">
                and t.deadline  = #{deadline}
            </if>
            <if test="status != null and status != ''">
                and t.status  = #{status}
            </if>
            <if test="rejectUnscreenableReason != null and rejectUnscreenableReason != ''">
                and t.reject_unscreenable_reason  like concat('%', #{rejectUnscreenableReason} ,'%')
            </if>

            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
        GROUP BY t.id
        ORDER BY t.serial_number ASC
    </select>

     <select id="selectOptionsByStandardSpecificationNumber" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.specification_number label,
        	t.id value
        from standard_specification t
        WHERE t.specification_number != '' and t.codex_torch_deleted = '0'
     </select>

    <select id="selectProductList1List" parameterType="com.huatek.frame.modules.business.service.dto.ProductListDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
        select
        <include refid="Base_Column_List" />,
        GROUP_CONCAT(s.specification_number) as standardSpecificationNumber
        from product_list t
        LEFT JOIN standard_specification s ON
        FIND_IN_SET(s.id, t.standard_specification_id) > 0
        OR s.id = t.standard_specification_id
        <where>
            and t.codex_torch_deleted = '0'
            <if test="evaluationOrderId != null and evaluationOrderId != ''">
                and t.evaluation_order_id = #{evaluationOrderId}
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                and t.serial_number  = #{serialNumber}
            </if>
            <if test="productModel != null and productModel != ''">
                and t.product_model  like concat('%', #{productModel} ,'%')
            </if>
            <if test="productName != null and productName != ''">
                and t.product_name  like concat('%', #{productName} ,'%')
            </if>
            <if test="productionBatch != null and productionBatch != ''">
                and t.production_batch  like concat('%', #{productionBatch} ,'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and t.manufacturer  like concat('%', #{manufacturer} ,'%')
            </if>
            <if test="quantity != null and quantity != ''">
                and t.quantity  = #{quantity}
            </if>

            <if test="taskLevel != null and taskLevel != ''">
                and t.task_level  = #{taskLevel}
            </if>
            <if test="testType != null and testType != ''">
                and t.test_type  = #{testType}
            </if>
            <if test="workOrderInspectionNumber != null and workOrderInspectionNumber != ''">
                and t.work_order_inspection_number  like concat('%', #{workOrderInspectionNumber} ,'%')
            </if>
            <if test="qualityGrade != null and qualityGrade != ''">
                and t.quality_grade  like concat('%', #{qualityGrade} ,'%')
            </if>
            <if test="sampleTotalCount != null and sampleTotalCount != ''">
                and t.sample_total_count  = #{sampleTotalCount}
            </if>
            <if test="experimentProject != null and experimentProject != ''">
                and t.experiment_project  like concat('%', #{experimentProject} ,'%')
            </if>
            <if test="specialAnalysisTestProject != null and specialAnalysisTestProject != ''">
                and t.special_analysis_test_project REGEXP #{specialAnalysisTestProject}
            </if>
            <if test="groupType != null and groupType != ''">
                and t.group_type REGEXP #{groupType}
            </if>
            <if test="inspectionTestProject != null and inspectionTestProject != ''">
                and t.inspection_test_project REGEXP #{inspectionTestProject}
            </if>
            <if test="qualityConsistencyTestItems != null and qualityConsistencyTestItems != ''">
                and t.quality_consistency_test_items REGEXP #{qualityConsistencyTestItems}
            </if>
            <if test="dpaTestProject != null and dpaTestProject != ''">
                and t.dpa_test_project REGEXP #{dpaTestProject}
            </if>
            <if test="otherTests != null and otherTests != ''">
                and t.other_tests  like concat('%', #{otherTests} ,'%')
            </if>
            <if test="technicalLiaisonName != null and technicalLiaisonName != ''">
                and t.technical_liaison_name  like concat('%', #{technicalLiaisonName} ,'%')
            </if>
            <if test="technicalLiaisonPhoneNumber != null and technicalLiaisonPhoneNumber != ''">
                and t.technical_liaison_phone_number  like concat('%', #{technicalLiaisonPhoneNumber} ,'%')
            </if>
            <if test="deadline != null">
                and t.deadline  = #{deadline}
            </if>
            <if test="status != null and status != ''">
                and t.status  = #{status}
            </if>
            <if test="rejectUnscreenableReason != null and rejectUnscreenableReason != ''">
                and t.reject_unscreenable_reason  like concat('%', #{rejectUnscreenableReason} ,'%')
            </if>

            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
        GROUP BY t.id
        ORDER BY t.serial_number ASC
	</select>

    <select id="selectProductListListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
		select
		<include refid="Base_Column_List" />, c.entrusted_unit as entrustedUnit, GROUP_CONCAT(s.specification_number) as standardSpecificationNumber, e.order_number as orderNumber
			from product_list t
        LEFT JOIN evaluation_order e ON t.evaluation_order_id = e.id
        LEFT JOIN standard_specification s ON FIND_IN_SET(s.id, t.standard_specification_id) > 0 OR s.id = t.standard_specification_id
        LEFT JOIN customer_information_management c ON c.id = e.customer_id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
                        #{id}
                    </foreach>
                </if>
            </where>
        group by t.id
	</select>
    <select id="selectProductListsByEvaluationOrderId"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
        select
        <include refid="Base_Column_List" />
        from product_list t
        where t.evaluation_order_id = #{evaluationOrderId}
    </select>
<!--    <select id="findProductListById" resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">-->
<!--        select-->
<!--        <include refid="Base_Column_List"/>, s.specification_number as specificationNumber-->
<!--        from product_list t-->
<!--        LEFT JOIN standard_specification s ON s.id = t.standard_specification_id-->
<!--        where t.id = #{id}-->
<!--    </select>-->
    <select id="findProductListById" resultType="com.huatek.frame.modules.business.domain.vo.ProductListVO">
        SELECT
        <include refid="Base_Column_List"/>,
        GROUP_CONCAT(s.specification_number) as specificationNumber,
        c.entrusted_unit as entrustedUnit,
        c.settlement_unit as settlementUnit,
        c.customer_manager as customerManager
        FROM product_list t
        LEFT JOIN standard_specification s
        ON FIND_IN_SET(s.id, t.standard_specification_id) > 0 OR s.id = t.standard_specification_id
        LEFT JOIN evaluation_order e ON e.id = t.evaluation_order_id
        LEFT JOIN customer_information_management c ON e.customer_id = c.id
        WHERE t.id = #{id}
        GROUP BY t.id
    </select>

    <select id="pagesawOrderProduct" parameterType="com.huatek.frame.modules.business.service.dto.SawOrderProductPageDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.SawOrderProductVO">
        select
        <include refid="Base_Column_List"/>,
        GROUP_CONCAT(s.specification_number) as qualityAssuranceBasis
        from product_list t
        LEFT JOIN standard_specification s ON
        FIND_IN_SET(s.id, t.standard_specification_id) > 0
        OR s.id = t.standard_specification_id
        <where>
            and t.codex_torch_deleted = '0'
            <if test="sawOrderId != null and sawOrderId != ''">
                AND t.saw_order_id = #{sawOrderId}
            </if>
            <if test="sawStatus != null and sawStatus != ''">
                AND t.saw_status = #{sawStatus}
            </if>
            <if test="productModel != null and productModel != ''">
                AND t.product_model LIKE CONCAT('%', #{productModel}, '%')
            </if>
            <if test="productName != null and productName != ''">
                AND t.product_name LIKE CONCAT('%', #{productName}, '%')
            </if>
            <if test="productionBatch != null and productionBatch != ''">
                AND t.production_batch like concat ('%', #{productionBatch}, '%')
            </if>
            <if test="qualityGrade != null and qualityGrade != ''">
                AND t.quality_grade like concat ('%', #{qualityGrade}, '%')
            </if>
        </where>

    </select>
</mapper>