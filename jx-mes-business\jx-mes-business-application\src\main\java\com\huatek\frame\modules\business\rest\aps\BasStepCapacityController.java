package com.huatek.frame.modules.business.rest.aps;

import com.alibaba.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO;
import com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO;
import com.huatek.frame.modules.business.service.dto.CapabilityReviewDTO;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import com.huatek.frame.modules.business.utils.MD5Util;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准产能管理
 */
@Api(tags = "aps")
@RestController
@RequestMapping("/api/stepcapacity")
public class BasStepCapacityController {

    @Autowired
    private HttpClientUtil httpClientUtil;
    @Log("获取标准产能分页信息")
    @ApiOperation(value = "获取标准产能分页信息")
    @PostMapping(value = "/page", produces = { "application/json;charset=utf-8" })
    public Object page(@RequestBody Object dto, HttpServletRequest request) throws Exception {
        InputParamDto inputParamDto=new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/stepcapacity/page");
        return httpClientUtil.callMes(request,inputParamDto);
    }

    @Log("获取所有标准产能信息")
    @ApiOperation(value = "获取所有标准产能信息")
    @PostMapping(value = "/findAll", produces = { "application/json;charset=utf-8" })
    public Object GetAllAsync(HttpServletRequest request) throws Exception {
        InputParamDto inputParamDto=new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/stepcapacity/getall");
        return httpClientUtil.callMes(request,inputParamDto);
    }

    @Log("详情")
    @ApiOperation(value = "详情")
    @GetMapping(value = "/{id}", produces = { "application/json;charset=utf-8" })
    public Object getDetail(@PathVariable String id, HttpServletRequest request) throws Exception {
        InputParamDto inputParamDto=new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/stepcapacity/"+id);
        return httpClientUtil.callMes(request,inputParamDto);
    }
    @Log("删除")
    @ApiOperation(value = "删除")
    @TorchPerm("standardCapacityManagement:delete")
    @DeleteMapping(value = "/delete/{id}", produces = { "application/json;charset=utf-8" })
    public Object delete(@PathVariable String id, HttpServletRequest request) {

        InputParamDto inputParamDto=new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("DELETE");
        inputParamDto.setServiceUrl("aps/api/stepcapacity/delete/"+id);
        return httpClientUtil.callMes(request,inputParamDto);

    }

    @Log("新增")
    @ApiOperation(value = "新增")
    @TorchPerm("standardCapacityManagement:add")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    public Object add(@RequestBody Object dto, HttpServletRequest request) throws Exception {
        InputParamDto inputParamDto=new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/stepcapacity/add");
        return httpClientUtil.callMes(request,inputParamDto);
    }
    @Log("修改")
    @ApiOperation(value = "修改")
    @PostMapping(value = "/edit", produces = { "application/json;charset=utf-8" })
    public Object edit(@RequestBody Object dto, HttpServletRequest request) throws Exception {
        InputParamDto inputParamDto=new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/stepcapacity/edit");
        return httpClientUtil.callMes(request,inputParamDto);
    }
}
