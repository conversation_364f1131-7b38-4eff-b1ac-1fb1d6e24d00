package com.huatek.frame.modules.business.rest.aps;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 设备工作日历
 */
@Api(tags = "设备工作日历")
@RestController
@RequestMapping("/api/deviceclass")
public class BasDeviceClassController {

    @Autowired
    private HttpClientUtil httpClientUtil;
    /**
     * 获取所有设备班次信息
     */
    @Log("获取所有设备班次信息")
    @ApiOperation(value = "获取所有设备班次信息")
    @PostMapping(value = "/findAll", produces = { "application/json;charset=utf-8" })
    public Object GetAllAsync(HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/deviceclass/getallasync");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 新增设备班次
     */
    @Log("新增设备班次")
    @ApiOperation(value = "新增设备班次")
    @PostMapping(value = "/createasync", produces = { "application/json;charset=utf-8" })
    public Object CreateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/deviceclass/createasync");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 删除设备班次
     */
    @Log("删除设备班次")
    @ApiOperation(value = "删除设备班次")
    @PostMapping(value = "/deleteasync/{id}", produces = { "application/json;charset=utf-8" })
    public Object CreateAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("PUT");
        inputParamDto.setServiceUrl("aps/api/deviceclass/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 修改设备班次
     */
    @Log("修改设备班次")
    @ApiOperation(value = "修改设备班次")
    @PutMapping(value = "/updateasync", produces = { "application/json;charset=utf-8" })
    public Object UpdateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("PUT");
        inputParamDto.setServiceUrl("aps/api/deviceclass/updateasync");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取设备班次列表分页
     */
    @Log("获取设备班次列表分页")
    @ApiOperation(value = "获取设备班次列表分页")
    @PostMapping(value = "/page", produces = { "application/json;charset=utf-8" })
    public Object GetPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/deviceclass/page");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取单个设备班次详情信息
     */
    @Log("获取单个设备班次详情信息")
    @ApiOperation(value = "获取单个设备班次详情信息")
    @PostMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetCurrentUserInfoAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/deviceclass/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }





}
