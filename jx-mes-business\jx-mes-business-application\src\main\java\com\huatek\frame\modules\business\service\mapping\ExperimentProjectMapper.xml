<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ExperimentProjectMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.process_scheme_name as processSchemeName,
		t.display_number as displayNumber,
        t.test_basis as testBasis,
<!--		t.process_code3 as processCode3,-->
		t.customer_process_name as customerProcessName,
		t.execution_sequence as executionSequence,
<!--		t.asso_wo_pred_proc as assoWoPredProc,-->
		t.test_conditions as testConditions,
		t.testing_times as testingTimes,
		t.duration_of_testing as durationOfTesting,
		t.judgment_criteria as judgmentCriteria,
<!--		t.workstation as workstation,-->

<!--		t.product_information1 as productInformation1,-->
		t.test_methodology as testMethodology,
<!--		t.testing_team as testingTeam,-->
		t.pda as pda,
		t.`grouping` as `grouping`,
		t.sample_quantity12 as sampleQuantity12,
		t.whether_to_include_in_scheduling as whetherToIncludeInScheduling,
		t.CODEX_TORCH_MASTER_FORM_ID as codexTorchMasterFormId,
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectExperimentProjectPage" parameterType="com.huatek.frame.modules.business.service.dto.ExperimentProjectDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ExperimentProjectVO">
		select
		<include refid="Base_Column_List" />,spm2.process_name2 as processCode3, spm.process_name2 AS assoWoPredProc,
        sg.group_name AS testingTeam,w.workstation_name as workstation,
        case when  pim.file_name is null then ss.specification_name else pim.file_name end as productInformation1
        from experiment_project t LEFT JOIN standard_process_management spm ON t.asso_wo_pred_proc = spm.id
        LEFT JOIN sys_group sg ON t.testing_team = sg.id left join standard_process_management spm2 on t.process_id =spm2.id
        left join workstation w on t.workstation  = w.id  left join standard_specification ss on t.product_information1 =ss.id
        left join product_information_management pim on t.product_information1 =pim.id
            <where>
                and 1=1
                <if test="processSchemeName != null and processSchemeName != ''">
                    and t.process_scheme_name  like concat('%', #{processSchemeName} ,'%')
                </if>
                <if test="displayNumber != null and displayNumber != ''">
                    and t.display_number  = #{displayNumber}
                </if>

                <if test="processCode3 != null and processCode3 != ''">
                    and t.process_code3  = #{processCode3}
                </if>
                <if test="customerProcessName != null and customerProcessName != ''">
                    and t.customer_process_name  like concat('%', #{customerProcessName} ,'%')
                </if>
                <if test="executionSequence != null and executionSequence != ''">
                    and t.execution_sequence  = #{executionSequence}
                </if>
<!--                <if test="assoWoPredProc != null and assoWoPredProc != ''">-->
<!--                    and t.asso_wo_pred_proc  = #{assoWoPredProc}-->
<!--                </if>-->
                <if test="testConditions != null and testConditions != ''">
                    and t.test_conditions  like concat('%', #{testConditions} ,'%')
                </if>
                <if test="testingTimes != null and testingTimes != ''">
                    and t.testing_times  like concat('%', #{testingTimes} ,'%')
                </if>
                <if test="durationOfTesting != null and durationOfTesting != ''">
                    and t.duration_of_testing  = #{durationOfTesting}
                </if>
                <if test="judgmentCriteria != null and judgmentCriteria != ''">
                    and t.judgment_criteria  like concat('%', #{judgmentCriteria} ,'%')
                </if>
                <if test="workstation != null and workstation != ''">
                    and t.workstation  like concat('%', #{workstation} ,'%')
                </if>

                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
<!--                <if test="testingTeam != null and testingTeam != ''">-->
<!--                    and t.testing_team  = #{testingTeam}-->
<!--                </if>-->
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="grouping != null and grouping != ''">
                    and t.grouping REGEXP #{grouping}
                </if>
                <if test="sampleQuantity12 != null and sampleQuantity12 != ''">
                    and t.sample_quantity12  = #{sampleQuantity12}
                </if>
                <if test="whetherToIncludeInScheduling != null and whetherToIncludeInScheduling != ''">
                    and t.whether_to_include_in_scheduling  = #{whetherToIncludeInScheduling}
                </if>
                <if test="codexTorchMasterFormId != null and codexTorchMasterFormId != ''">
                    and t.CODEX_TORCH_MASTER_FORM_ID  like concat('%', #{codexTorchMasterFormId} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>
    <select id="selectOptionsByProcessCode3" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.process_name2 label,
        t.id value
        from standard_process_management t
        WHERE t.step_number != ''
    </select>
    <select id="selectDataLinkageByProcessId" parameterType="String"
            resultType="java.util.Map">
        select
        t.test_basis as testBasis,
        t.testing_times as testingTimes,
        t.duration_of_testing as durationOfTesting,
        t.judgment_criteria as judgmentCriteria
        from standard_process_management t
        WHERE t.id = #{step_number}
    </select>
    <select id="selectOptionsByAssoWoPredProc" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.process_name2 label,
        t.id value
        from standard_process_management t
        WHERE t.step_number != ''
    </select>
    <select id="selectOptionsByWorkstation" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.workstation_name label,
        t.id value
        from workstation t
        WHERE t.workstation_number != ''
    </select>

    <select id="selectOptionsByProductInformation1" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.specification_name label,
        t.id value
        from standard_specification t
        WHERE t.specification_number != ''
    </select>
    <select id="selectOptionsByTestingTeam" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.group_name label,
        t.id value
        from sys_group t
        WHERE t.group_code != ''
    </select>

    <select id="selectOptionsByProductModel" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.product_model label,
        t.id value
        from product_management   t
        WHERE t.CODEX_TORCH_DELETED ='0'
    </select>

    <select id="selectDataLinkageByProductModel" parameterType="String"
            resultType="java.util.Map">
        select
        t.product_name as productName,t.product_category as procuctCategory ,t.manufacturer as manufacturer
        from product_management t
        WHERE t.id = #{productModel}
    </select>

    <select id="selectExperimentProjectList" parameterType="com.huatek.frame.modules.business.service.dto.ExperimentProjectDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ExperimentProjectVO">
		select
		<include refid="Base_Column_List" />,spm2.process_name2 as processCode3, spm.process_name2 AS assoWoPredProc,
        sg.group_name AS testingTeam,w.workstation_name as workstation,
        case when  pim.file_name is null then ss.specification_name else pim.file_name end as productInformation1
        from experiment_project t LEFT JOIN standard_process_management spm ON t.asso_wo_pred_proc = spm.id
        LEFT JOIN sys_group sg ON t.testing_team = sg.id left join standard_process_management spm2 on t.process_id =spm2.id
        left join workstation w on t.workstation  = w.id  left join standard_specification ss on t.product_information1 =ss.id
        left join product_information_management pim on t.product_information1 =pim.id
            <where>
                and 1=1
                <if test="processSchemeName != null and processSchemeName != ''">
                    and t.process_scheme_name  like concat('%', #{processSchemeName} ,'%')
                </if>
                <if test="displayNumber != null and displayNumber != ''">
                    and t.display_number  = #{displayNumber}
                </if>

                <if test="processCode3 != null and processCode3 != ''">
                    and t.process_code3  = #{processCode3}
                </if>
                <if test="customerProcessName != null and customerProcessName != ''">
                    and t.customer_process_name  like concat('%', #{customerProcessName} ,'%')
                </if>
                <if test="executionSequence != null and executionSequence != ''">
                    and t.execution_sequence  = #{executionSequence}
                </if>
<!--                <if test="assoWoPredProc != null and assoWoPredProc != ''">-->
<!--                    and t.asso_wo_pred_proc  = #{assoWoPredProc}-->
<!--                </if>-->
                <if test="testConditions != null and testConditions != ''">
                    and t.test_conditions  like concat('%', #{testConditions} ,'%')
                </if>
                <if test="testingTimes != null and testingTimes != ''">
                    and t.testing_times  like concat('%', #{testingTimes} ,'%')
                </if>
                <if test="durationOfTesting != null and durationOfTesting != ''">
                    and t.duration_of_testing  = #{durationOfTesting}
                </if>
                <if test="judgmentCriteria != null and judgmentCriteria != ''">
                    and t.judgment_criteria  like concat('%', #{judgmentCriteria} ,'%')
                </if>
                <if test="workstation != null and workstation != ''">
                    and t.workstation  like concat('%', #{workstation} ,'%')
                </if>

                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
<!--                <if test="testingTeam != null and testingTeam != ''">-->
<!--                    and t.testing_team  = #{testingTeam}-->
<!--                </if>-->
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="grouping != null and grouping != ''">
                    and t.grouping REGEXP #{grouping}
                </if>
                <if test="sampleQuantity12 != null and sampleQuantity12 != ''">
                    and t.sample_quantity12  = #{sampleQuantity12}
                </if>
                <if test="whetherToIncludeInScheduling != null and whetherToIncludeInScheduling != ''">
                    and t.whether_to_include_in_scheduling  = #{whetherToIncludeInScheduling}
                </if>
                <if test="codexTorchMasterFormId != null and codexTorchMasterFormId != ''">
                    and t.CODEX_TORCH_MASTER_FORM_ID  like concat('%', #{codexTorchMasterFormId} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectExperimentProjectListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ExperimentProjectVO">
		select
		<include refid="Base_Column_List" />,spm2.process_name2 as processCode3, spm.process_name2 AS assoWoPredProc,
        sg.group_name AS testingTeam,w.workstation_name as workstation,
        case when  pim.file_name is null then ss.specification_name else pim.file_name end as productInformation1
        from experiment_project t LEFT JOIN standard_process_management spm ON t.asso_wo_pred_proc = spm.id
        LEFT JOIN sys_group sg ON t.testing_team = sg.id left join standard_process_management spm2 on t.process_id =spm2.id
        left join workstation w on t.workstation  = w.id  left join standard_specification ss on t.product_information1 =ss.id
        left join product_information_management pim on t.product_information1 =pim.id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>