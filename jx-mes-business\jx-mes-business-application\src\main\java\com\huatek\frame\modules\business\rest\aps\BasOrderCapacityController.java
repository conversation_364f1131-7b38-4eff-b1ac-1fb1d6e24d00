package com.huatek.frame.modules.business.rest.aps;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 工单产能管理
 */

@Api(tags = "工单产能管理")
@RestController
@RequestMapping("/api/ordercapacity")
public class BasOrderCapacityController {

    @Autowired
    private HttpClientUtil httpClientUtil;
    /**
     * 获取工单产能分页信息
     */
    @Log("获取工单产能分页信息")
    @ApiOperation(value = "获取工单产能分页信息")
    @PostMapping(value = "/page", produces = { "application/json;charset=utf-8" })
    public Object GetPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/ordercapacity/page");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 根据ID获取工单产能详情
     */
    @Log("根据ID获取工单产能详情")
    @ApiOperation(value = "根据ID获取工单产能详情")
    @GetMapping(value = "/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/ordercapacity/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 插入工单产能模型信息
     */
    @Log("插入工单产能模型信息")
    @ApiOperation(value = "插入工单产能模型信息")
    @PostMapping(value = "/choosestep", produces = { "application/json;charset=utf-8" })
    public Object CreateStepAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/ordercapacity/choosestep");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 更新工单产能信息
     */
    @Log("更新工单产能信息")
    @ApiOperation(value = "更新工单产能信息")
    @PostMapping(value = "/update", produces = { "application/json;charset=utf-8" })
    public Object UpdateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/ordercapacity/update");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取工单工序主资源
     */
    @Log("获取工单工序主资源")
    @ApiOperation(value = "获取工单工序主资源")
    @PostMapping(value = "/mainsource/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetMainSourceAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/mainsource/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 保存工单工序主资源
     */
    @Log("保存工单工序主资源")
    @ApiOperation(value = "保存工单工序主资源")
    @PostMapping(value = "/mainsource", produces = { "application/json;charset=utf-8" })
    public Object SaveMainSourceAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/ordercapacity/mainsource");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取工单工序辅助资源
     */
    @Log("获取工单工序辅助资源")
    @ApiOperation(value = "获取工单工序辅助资源")
    @PostMapping(value = "/assistantsource/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetAssistantSourceAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/assistantsource/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 保存工单工序辅助资源
     */
    @Log("保存工单工序辅助资源")
    @ApiOperation(value = "保存工单工序辅助资源")
    @PostMapping(value = "/assistantsource", produces = { "application/json;charset=utf-8" })
    public Object SaveAssistantSourceAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/ordercapacity/assistantsource");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 批量下达订单
     */
    @Log("批量下达订单")
    @ApiOperation(value = "批量下达订单")
    @PostMapping(value = "/issue", produces = { "application/json;charset=utf-8" })
    public Object OrderIssueAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/ordercapacity/issue");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 转模型库
     */
    @Log("转模型库")
    @ApiOperation(value = "转模型库")
    @PostMapping(value = "/turnlibrary", produces = { "application/json;charset=utf-8" })
    public Object TurnToLibraryAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/ordercapacity/turnlibrary");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 待排工单管理列表
     */
    @Log("待排工单管理列表")
    @ApiOperation(value = "待排工单管理列表")
    @PostMapping(value = "/workOrderList", produces = { "application/json;charset=utf-8" })
    public Object GetWorkOrderPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/ordercapacity/workOrderList");
        return httpClientUtil.callMes(request, inputParamDto);
    }



}
