package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.DeviceTraceability;
import com.huatek.frame.modules.business.domain.DeviceType;
import com.huatek.frame.modules.business.domain.EquipmentInventory;
import com.huatek.frame.modules.business.domain.StandardProcessPlan;

import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;

import com.huatek.frame.modules.business.domain.vo.DeviceInfoVO;
import com.huatek.frame.modules.business.domain.vo.EquipmentInventorySampleVO;
import com.huatek.frame.modules.business.mapper.DeviceTraceabilityMapper;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.business.service.DeviceTraceabilityService;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.Dic;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
//import com.huatek.frame.modules.system.mapper.SysGroupMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.vo.EquipmentInventoryVO;
import com.huatek.frame.modules.business.mapper.EquipmentInventoryMapper;
import com.huatek.frame.modules.business.service.EquipmentInventoryService;

import com.huatek.frame.modules.business.mapper.StandardProcessPlanMapper;
import com.huatek.frame.modules.business.mapper.DeviceTypeMapper;

import org.springframework.util.CollectionUtils;

import com.huatek.frame.modules.business.domain.vo.TraceInformationVO;
import com.huatek.frame.modules.business.service.TraceInformationService;


/**
 * 设备台账 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-18
 */
//@Service
@DubboService
//@CacheConfig(cacheNames = "equipmentInventory")
//@RefreshScope
@Slf4j
public class EquipmentInventoryServiceImpl implements EquipmentInventoryService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private EquipmentInventoryMapper equipmentInventoryMapper;

	@Autowired
    private StandardProcessPlanMapper standardProcessPlanMapper;
	@Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private DeviceTraceabilityMapper deviceTraceabilityMapper;

    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    private DeviceTraceabilityService deviceTraceabilityService;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    @Autowired
    private TraceInformationService traceInformationService;


	public EquipmentInventoryServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<EquipmentInventoryVO>> findEquipmentInventoryPage(EquipmentInventoryDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<EquipmentInventoryVO> equipmentInventorys = equipmentInventoryMapper.selectEquipmentInventoryPage(dto);
		TorchResponse<List<EquipmentInventoryVO>> response = new TorchResponse<List<EquipmentInventoryVO>>();
		response.getData().setData(equipmentInventorys);
		response.setStatus(200);
		response.getData().setCount(equipmentInventorys.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(EquipmentInventoryDTO equipmentInventoryDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(equipmentInventoryDto.getCodexTorchDeleted())) {
            equipmentInventoryDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        //todo 如果是金蝶同步，从同步信息获取

        String id = equipmentInventoryDto.getId();
		EquipmentInventory entity = new EquipmentInventory();
        BeanUtils.copyProperties(equipmentInventoryDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));


		if (HuatekTools.isEmpty(id)) {
            TorchResponse response =  codeManagementService.getOrderNumber("SBBM");
            entity.setDeviceSerialNumber(response.getData().getData().toString());
			equipmentInventoryMapper.insert(entity);
		} else {
			equipmentInventoryMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        EquipmentInventoryVO vo = new EquipmentInventoryVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<EquipmentInventoryVO> findEquipmentInventory(String id) {
		EquipmentInventoryVO vo = new EquipmentInventoryVO();
		if (!HuatekTools.isEmpty(id)) {
			EquipmentInventory entity = equipmentInventoryMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<EquipmentInventoryVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		equipmentInventoryMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("manufacturer",equipmentInventoryMapper::selectOptionsByManufacturer);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("deviceTypeCode",equipmentInventoryMapper::selectOptionsByDeviceType);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("belongingGroup",equipmentInventoryMapper::selectOptionsByBelongingGroup);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "equipment_inventory", convertorFields = "trace_information_d0#tracingMethod,deviceCategory,status")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<EquipmentInventoryVO> selectEquipmentInventoryList(EquipmentInventoryDTO dto) {
        return equipmentInventoryMapper.selectEquipmentInventoryList(dto);
    }

    /**
     * 导入设备台账数据
     *
     * @param equipmentInventoryList 设备台账数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "equipment_inventory", convertorFields = "d0,deviceCategory,status")
    public TorchResponse importEquipmentInventory(List<EquipmentInventoryVO> equipmentInventoryList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(equipmentInventoryList) || equipmentInventoryList.size() == 0) {
            throw new ServiceException("导入设备台账数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (EquipmentInventoryVO vo : equipmentInventoryList) {
            try {
                EquipmentInventory equipmentInventory = new EquipmentInventory();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, equipmentInventory);
                QueryWrapper<EquipmentInventory> wrapper = new QueryWrapper();
                EquipmentInventory oldEquipmentInventory = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = EquipmentInventoryVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<EquipmentInventory> oldEquipmentInventoryList = equipmentInventoryMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldEquipmentInventoryList) && oldEquipmentInventoryList.size() > 1) {
                        equipmentInventoryMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldEquipmentInventoryList) && oldEquipmentInventoryList.size() == 1) {
                        oldEquipmentInventory = oldEquipmentInventoryList.get(0);
                    }
                }
                if (StringUtils.isNull(oldEquipmentInventory)) {
                    BeanValidators.validateWithException(validator, vo);
                    equipmentInventoryMapper.insert(equipmentInventory);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、设备编号 " + vo.getDeviceSerialNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldEquipmentInventory, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    equipmentInventoryMapper.updateById(oldEquipmentInventory);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、设备编号 " + vo.getDeviceSerialNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、设备编号 " + vo.getDeviceSerialNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、设备编号 " + vo.getDeviceSerialNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(EquipmentInventoryVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getDeviceSerialNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>设备编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getAssetNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>资产编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getD0())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>是否金蝶同步不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getD03())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>出厂编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getSpecificationModel())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>规格型号不能为空!");
        }
        if (!HuatekTools.isEmpty(vo.getManufacturer())) {
            List<String> manufacturerList = Arrays.asList(vo.getManufacturer().split(","));
            List<StandardProcessPlan> list = standardProcessPlanMapper.selectList(new QueryWrapper<StandardProcessPlan>().in("manufacturer", manufacturerList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("生产厂家=" + vo.getManufacturer() + "; ");
            }
        }
        if (HuatekTools.isEmpty(vo.getDeviceTypeCode())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>设备类型不能为空!");
        }
        if (!HuatekTools.isEmpty(vo.getDeviceTypeCode())) {
            List<String> deviceTypeList = Arrays.asList(vo.getDeviceTypeCode().split(","));
            List<DeviceType> list = deviceTypeMapper.selectList(new QueryWrapper<DeviceType>().in("device_type_code", deviceTypeList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("设备类型=" + vo.getDeviceTypeCode() + "; ");
            }
        }
        if (HuatekTools.isEmpty(vo.getBelongingGroup())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>所属分组不能为空!");
        }
//        if (!HuatekTools.isEmpty(vo.getBelongingGroup())) {
//            List<String> belongingGroupList = Arrays.asList(vo.getBelongingGroup().split(","));
//            List<SysGroup> list = sysGroupMapper.selectList(new QueryWrapper<SysGroup>().in("group_code", belongingGroupList));
//            if (CollectionUtils.isEmpty(list)) {
//                failureRecord++;
//                failureRecordMsg.append("所属分组=" + vo.getBelongingGroup() + "; ");
//            }
//        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectEquipmentInventoryListByIds(List<String> ids) {
        List<EquipmentInventoryVO> equipmentInventoryList = equipmentInventoryMapper.selectEquipmentInventoryListByIds(ids);

		TorchResponse<List<EquipmentInventoryVO>> response = new TorchResponse<List<EquipmentInventoryVO>>();
		response.getData().setData(equipmentInventoryList);
		response.setStatus(200);
		response.getData().setCount((long)equipmentInventoryList.size());
		return response;
    }

    /**
     * 设备台账主子表单组合提交
     *
	 * @param equipmentInventoryDto 设备台账DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitMasterDetails(EquipmentInventoryDTO equipmentInventoryDto){
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(equipmentInventoryDto.getCodexTorchDeleted())) {
            equipmentInventoryDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        //非必要字段处理
//        equipmentInventoryDto.setId("");
        equipmentInventoryDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

        equipmentInventoryDto.setCodexTorchDetailItemIds("");


        TorchResponse<EquipmentInventoryVO> masterSubmitResp = this.saveOrUpdate(equipmentInventoryDto);
        EquipmentInventoryVO masterVo = masterSubmitResp.getData().getData();

        List<TraceInformationDTO> traceInformationDTOs = new ArrayList<>();
        if (equipmentInventoryDto.getDetailFormItems() != null && equipmentInventoryDto.getDetailFormItems().length > 0) {
            traceInformationDTOs = Arrays.asList(equipmentInventoryDto.getDetailFormItems());
        }
//        else if (StringUtils.isNotEmpty(equipmentInventoryDto.getCodexTorchDetailItemIds())) {
//        } else {
//            throw new ServiceException("表单提交异常，表单明细项为空");
//        }

        for(TraceInformationDTO traceInformationDto : traceInformationDTOs){
            traceInformationDto.setId("");

            //非必要字段处理
            traceInformationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

//            traceInformationDto.setEquipmentInventoryId(masterVo.getId());
            //主子表关联ID
            traceInformationDto.setCodexTorchMasterFormId(masterVo.getId());
            // 业务字段管理
            traceInformationDto.setDeviceSerialNumber(masterVo.getDeviceSerialNumber());
            //提交
            TorchResponse<TraceInformationVO> detailSubmitResp = traceInformationService.saveOrUpdate(traceInformationDto);
            TraceInformationVO detailVo = detailSubmitResp.getData().getData();

            //更新设备溯源数据
            DeviceTraceabilityDTO deviceTraceability = DeviceTraceabilityDTO.builder()
                    .equipmentInventoryId(masterVo.getId())
                    .deviceSerialNumber(masterVo.getDeviceSerialNumber())
                    .specificationModel(masterVo.getSpecificationModel())
                    .deviceName(masterVo.getDeviceName())
                    .scheduledTraceabilityDate(traceInformationDto.getTracebackValidityPeriod())
                    .tracebackScheme(traceInformationDto.getTracebackScheme())
                    .status(DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_UN_FINISHED)
                    .build();
            deviceTraceabilityService.saveOrUpdate(deviceTraceability);

        }

		TorchResponse response = new TorchResponse();
        response.getData().setData(masterVo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }

    /**
     * 查询设备最新的溯源记录
     * @param deviceSerialNumber
     * @return
     */
    @Override
    public TorchResponse getLatestTraceInfo(String deviceSerialNumber) {
        TorchResponse response = traceInformationService.getLatestTraceInfo(deviceSerialNumber);
        return response;
    }

    /**
     * 查询相同设备类型的设备信息列表
     * @param requestParam 设备类型
     * @return
     */
    @Override
    public TorchResponse findDeviceListByType(String requestParam) {
        List<DeviceInfoVO> deviceInfoVOList = equipmentInventoryMapper.findDeviceListByType(requestParam);
        TorchResponse response = new TorchResponse();
        response.getData().setData(deviceInfoVOList);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount((long)deviceInfoVOList.size());
        return response;

    }

    @Override
    public TorchResponse updateStatus(EquipmentInventoryUpdateStatusDTO requestParam) {

        equipmentInventoryMapper.updateStatus(requestParam);

        TorchResponse response = new TorchResponse();
        return  response;

    }

    @Override
    public TorchResponse calibrationRate() {
        //计算出设备状态为已完成和已过期的数量，然后求设备即时校准率
        LambdaQueryWrapper<DeviceTraceability> onTimeQueryWrapper = Wrappers.lambdaQuery(DeviceTraceability.class)
                .eq(DeviceTraceability::getStatus, DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_FINISHED);

        LambdaQueryWrapper<DeviceTraceability> expiredQueryWrapper = Wrappers.lambdaQuery(DeviceTraceability.class)
                .eq(DeviceTraceability::getStatus, DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_EXPIRED);
        Long onTimeCompletion = deviceTraceabilityMapper.selectCount(onTimeQueryWrapper);
        Long expired = deviceTraceabilityMapper.selectCount(expiredQueryWrapper);

        // 计算校准率（避免除以零）
        BigDecimal calibrationRate;
        long total = onTimeCompletion + expired;
        String calibrationRateStr;
        if (total == 0) {
            // 没有数据时默认返回-
            calibrationRateStr = "-";
        } else {
            // 计算比例并转换为百分数（乘以100）
            calibrationRate = new BigDecimal(onTimeCompletion)
                    .divide(new BigDecimal(total), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP);
            calibrationRateStr = calibrationRate + "%";
        }

        TorchResponse response = new TorchResponse();
        response.getData().setData(calibrationRateStr);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;


    }

    @Override
    public TorchResponse<List<EquipmentInventorySampleVO>> getAllEquipmentInventory() {
        List<EquipmentInventorySampleVO> equipmentInventoryList = equipmentInventoryMapper.getAllEquipmentInventory();

        TorchResponse<List<EquipmentInventorySampleVO>> response = new TorchResponse<List<EquipmentInventorySampleVO>>();
        response.getData().setData(equipmentInventoryList);
        response.setStatus(200);
        response.getData().setCount((long)equipmentInventoryList.size());
        return response;
    }

}
