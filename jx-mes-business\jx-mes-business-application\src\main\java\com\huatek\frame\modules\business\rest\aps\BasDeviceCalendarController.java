package com.huatek.frame.modules.business.rest.aps;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 设备日历管理
 */
@Api(value = "设备日历管理")
@RestController
@RequestMapping("/api/devicecalendar")
public class BasDeviceCalendarController {

    @Autowired
    private HttpClientUtil httpClientUtil;
    /**
     * 新增设备日历
     */
    @Log("新增设备日历")
    @ApiOperation(value = "新增设备日历")
    @TorchPerm("deviceCalendarManagement:add")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    public Object CreateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/devicecalendar");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 新增设备日历
     */
    @Log("删除设备日历")
    @ApiOperation(value = "删除设备日历")
    @TorchPerm("deviceCalendarManagement:delete")
    @PostMapping(value = "/delete/{id}", produces = { "application/json;charset=utf-8" })
    public Object DeleteAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/devicecalendar/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 设备日历分页查询
     */
    @Log("设备日历分页查询")
    @ApiOperation(value = "设备日历分页查询")
    @PostMapping(value = "/paged", produces = { "application/json;charset=utf-8" })
    public Object GetPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/devicecalendar/paged");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取所有设备日历
     */
    @Log("获取所有设备日历")
    @ApiOperation(value = "获取所有设备日历")
    @PostMapping(value = "/findAll", produces = { "application/json;charset=utf-8" })
    public Object GetAllAsync(HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/devicecalendar");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取单个设备日历
     */
    @Log("获取单个设备日历")
    @ApiOperation(value = "获取单个设备日历")
    @PostMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/devicecalendar/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取单个设备日历
     */
    @Log("获取设备日历下拉列表")
    @ApiOperation(value = "获取设备日历下拉列表")
    @PostMapping(value = "/calendardrops", produces = { "application/json;charset=utf-8" })
    public Object GetCalendarDropAsync(HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/devicecalendar/calendardrops");
        return httpClientUtil.callMes(request, inputParamDto);
    }



}
