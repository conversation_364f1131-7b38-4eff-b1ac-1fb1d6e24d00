package com.huatek.frame.modules.business.constant;

import java.io.File;

public class BusinessConstant {


    //能力资产编号
    public static final String CAPABILITY_VERIFICATION_NLZC = "NLZC";
    //能力开发任务编号
    public static final String CAPABILITY_VERIFICATION_NLKF = "NLKF";
    //生产任务编号
    public static final String CAPABILITY_SCRW = "SCRW";
    //生产部班组长 角色
    public static final String ROLE_SHENGCHANBUBANZUZHANG = "生产部班组长";
    //可靠性班组长 角色
    public static final String ROLE_KEKAOXINGBANZUZHANG = "可靠性班组长";
    //任务审批人 角色
    public static final String ROLE_RENWUSHENPIREN = "任务审批人";
    //待办
    public static final String PRODUCTIONTASK_TODO = "0";
    //全部
    public static final String PRODUCTIONTASK_ALL = "1";

    public static final  String ADMIN="admin";
    public static final String PRODUCTION_TASK_MANAGE_MENU_NAME = "生产管理-生产任务管理";
    public static final String CAPABILITY_REVIEW_MENU_NAME = "能力评审";

    public static final String CAPABILITY_REVIEW_FILE = "文件评审";
    //PDA工序名称
    public static final String PDA_PROCESS_NAME = "PDA";
    //市场结算角色
    public static final String ROLE_SHICHANGJIESUAN = "市场结算";

    public static String RESOURCESPATH = System.getProperty("user.dir") + File.separator + "jx-mes-business" + File.separator + "jx-mes-business-application" + File.separator + "resources" + File.separator;

    public static String LOGOPATH = RESOURCESPATH + "logo.png";
    public static String TEMPLATEPATH = RESOURCESPATH +"template"+ File.separator;

    public static final String DPA = "DPA";

}
