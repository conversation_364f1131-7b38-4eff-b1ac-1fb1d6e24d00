package com.huatek.tool.modules.poi;

import java.io.File;
import java.io.FileInputStream;

import org.apache.poi.util.StringUtil;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class TempData {

	public static JSONObject createTempData() {
		JSONObject jsonData = new JSONObject();
		jsonData.put("p0", "K202401196(07)");
		jsonData.put("p1", "微波混频器");
		jsonData.put("p2", "HC701");
		jsonData.put("p3", "DPA分析");
		jsonData.put("p4", "广州瀚辰");
		jsonData.put("p5", "河北晶禾电子技术股份有限公司");
		jsonData.put("p6", "合格");
		jsonData.put("p7", "029-89697221-606");
		jsonData.put("p8", "029-89697221-610");
		jsonData.put("p9", "029-89697221-608");
		jsonData.put("p10", "710199");
		jsonData.put("p11", "陕西省西安市高新区毕原二路3号先导院南区7号楼、10号楼");
		jsonData.put("p12", "杨帆");
		jsonData.put("p13", "2024年01月30日");
		jsonData.put("p14", "杨帆");
		jsonData.put("p15", "2024年01月30日");
		jsonData.put("p16", "杨帆");
		jsonData.put("p17", "2024年01月30日");
		jsonData.put("p18", "2024年01月25日");
		jsonData.put("p19", "2024年01月30日");
		jsonData.put("p20", "微波混频器");
		jsonData.put("p21", "HC701");
		jsonData.put("p22", "HF2224");
		jsonData.put("p23", "4");
		jsonData.put("p24", "/");
		jsonData.put("p25", "工业级");
		jsonData.put("p26", "客户送样");
		jsonData.put("p27", "6-12");
		jsonData.put("p28", "2024年01月30日");
		jsonData.put("p29", "HC701");
		jsonData.put("p30", "HC702");
	    jsonData.put("p31", "商品价格清单");
	    jsonData.put("p32", createTable());
	    
        JSONArray records = new JSONArray();
        JSONObject r0 = createRecord(0);
        JSONObject r1 = createRecord(1);
        JSONObject r2 = createRecord(2);
        r0.put("result", loopRow());
        
        JSONObject df = new JSONObject(); 
        df.put("content", "无异常，合格。");
        df.put("config", new StyleConfig(300, null, null, null, null, new Integer[]{5, 5, 5, 5}));
        
        r1.put("defaultResult", df);
        r2.put("result", loopRow());
		records.add(r0);
		records.add(r1);
		records.add(r2);
		jsonData.put("records", records);
	    
	    return jsonData;
	}
	
	private static JSONObject createTable() {
		JSONObject tab = new JSONObject();
	    JSONArray thead = new JSONArray();
	    JSONArray tbody = new JSONArray();
	    tab.put("dataType", "table");
	    tab.put("thead", thead);
	    tab.put("tbody", tbody);
	    JSONObject th1 = new JSONObject(); th1.put("name", "序号"); th1.put("code", "index"); thead.add(th1);
	    JSONObject th2 = new JSONObject(); th2.put("name", "编码"); th2.put("code", "code"); thead.add(th2);
	    JSONObject th3 = new JSONObject(); th3.put("name", "名称"); th3.put("code", "name"); thead.add(th3);
	    JSONObject th4 = new JSONObject(); th4.put("name", "售价"); th4.put("code", "price"); thead.add(th4);
	    
	    JSONObject tr1 = new JSONObject(); tr1.put("index", "1"); tr1.put("code", "NO001"); tr1.put("name", "商品1"); tr1.put("price", 1.0); tbody.add(tr1);
	    JSONObject tr2 = new JSONObject(); tr2.put("index", "2"); tr2.put("code", "NO002"); tr2.put("name", "商品2"); tr2.put("price", 1.0); tbody.add(tr2);
	    JSONObject tr3 = new JSONObject(); tr3.put("index", "3"); tr3.put("code", "NO003"); tr3.put("name", "商品3"); tr3.put("price", 1.0); tbody.add(tr3);
	    JSONObject tr4 = new JSONObject(); tr4.put("index", "4"); tr4.put("code", "NO004"); tr4.put("name", "商品4"); tr4.put("price", 1.0); tbody.add(tr4);
	    JSONObject tr5 = new JSONObject(); tr5.put("index", "5"); tr5.put("code", "NO005"); tr5.put("name", "商品5"); tr5.put("price", 1.0); tbody.add(tr5);
	    JSONObject tr6 = new JSONObject(); tr6.put("index", "6"); tr6.put("code", "NO006"); tr6.put("name", "商品6"); tr6.put("price", 1.0); tbody.add(tr6);
	    JSONObject tr7 = new JSONObject(); tr7.put("index", "7"); tr7.put("code", "NO007"); tr7.put("name", "商品7"); tr7.put("price", 1.0); tbody.add(tr7);
	    JSONObject tr8 = new JSONObject(); tr8.put("index", "8"); tr8.put("code", "NO008"); tr8.put("name", "商品8"); tr8.put("price", 1.0); tbody.add(tr8);
	    JSONObject tr9 = new JSONObject(); tr9.put("index", "9"); tr9.put("code", "NO009"); tr9.put("name", "商品9"); tr9.put("price", 1.0); tbody.add(tr9);
	    return tab;
	} 
	
	
	private static JSONObject createRecord(int index) {
		String[] datas = new String[]{
			"微波混频器|HC701|HF2224|外部目检|23℃～27℃|40%RH～60%RH|立体显微镜|SMZ745T|/|/|JXYQ022|/|2025/09/26|/|GJB548B-2005 方法2009.1|4|GJB4027A-2006 项目1103 塑封半导体集成电路|本项目分析4只合格，0只不合格。|/|孙越海|2024/01/26|杨  帆|2024/01/26"
			, "微波混频器|HC701|HF2224|X射线检查|23℃～27℃|40%RH～60%RH|X光检测系统|EVO|/|/|JXYQ242|/|2025/09/26|/|GJB548B-2005 方法2009.1|4|GJB4027A-2006 项目1103 塑封半导体集成电路|本项目分析4只合格，0只不合格。|样品X射线检查均合格，只附其中1只样品的检查照片。|王骁勇|2024/01/26|杨  帆|2024/01/26"
			, "微波混频器|HC701|HF2224|声学扫描显微镜检查|23℃～27℃|40%RH～60%RH|超声波扫描检测系统|SONOSCAN D9650|/|/|JXYQ007|/|2025/09/26|/|GJB548B-2005 方法2009.1|4|GJB4027A-2006 项目1103 塑封半导体集成电路|本项目分析4只合格，0只不合格。|/|贠  丹|2024/01/26|杨  帆|2024/01/26"
		
		};
		JSONObject record = new JSONObject();
		String[] r1 = datas[index].split("\\|");
		for(int i = 0; i < r1.length; i++) {
			record.put("rp" + (i + 1), r1[i]);
		}
		return record;
	}
	
	public static JSONObject loopRow() {
		JSONObject row = new JSONObject();
		JSONObject config = new JSONObject();
		config.put("cellSpans", new int[]{3, 3});
		config.put("margin", new int[]{3, 3, 3, 3});
		config.put("rowHeights", new Integer[]{150, 15});
		row.put("config", config);
		JSONArray datas = new JSONArray();
		JSONObject d1 = createRowData("","样品正面X射线检查照片，无异常");
		JSONObject d2 = createRowData("","样品正面局部X射线检查照片，无异常");
		JSONObject d3 = createRowData("","样品侧面X射线检查照片，无异常");
		JSONObject d4 = createRowData("","样品侧面局部X射线检查照片，无异常");
		datas.add(d1);
		datas.add(d2);
		datas.add(d3);
		datas.add(d4);
		row.put("datas", datas);
		return row;
	}
	
	private static JSONObject createRowData(String img, String remark) {
		JSONObject data = new JSONObject();
		JSONArray dr = new JSONArray();
		
		JSONObject image = new JSONObject(); dr.add(image);
		image.put("dataType", "image");
		if(StringUtil.isNotBlank(img)) {
			image.put("content", readImage(img));
			image.put("w", 200);
			image.put("h", 150);
		}
		
		JSONObject explan = new JSONObject(); dr.add(explan);
		explan.put("dataType", "string");
		explan.put("height", 30);
		explan.put("content", remark);
		
		data.put("rows", dr);
		return data;
	}
	
	private static byte[] readImage(String img) {
		try {
			File file = new File(img);
			FileInputStream fis = new FileInputStream(file);
			byte[] bs = new byte[(int)file.length()];
			fis.read(bs);
			fis.close();
			return bs;
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("文件未找到");
		}
		return new byte[0];
	}
	
}
