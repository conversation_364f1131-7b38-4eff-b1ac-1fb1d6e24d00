<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.CapabilityAssetMapper">
    <sql id="Base_Column_List">
        t.id as id,
        t.capability_number as capabilityNumber,
        t.capability_type as capabilityType,
        t.applicable_test_type as applicableTestType,
        t.product_model as productModel,
        t.product_name as productName,
        t.manufacturer as manufacturer,
        t.product_category as productCategory,
        t.task_number as taskNumber,
        t.applicable_equipment_type as applicableEquipmentType,
        t.program_number as programNumber,
        t.product_information1 as productInformation1,
        t.board_aging_capability as boardAgingCapability,
        t.total_board_aging_capability as totalBoardAgingCapability,
        t.codex_torch_creator_id as codexTorchCreatorId,
        t.codex_torch_updater as codexTorchUpdater,
        t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_create_datetime as codexTorchCreateDatetime,
        t.codex_torch_update_datetime as codexTorchUpdateDatetime,
        t.codex_torch_deleted as codexTorchDeleted
    </sql>
    <sql id="Base_Column_List2">
        t.id as id,
        t.capability_number as capabilityNumber,
        t.capability_type as capabilityType,
        t.applicable_test_type as applicableTestType,
        t.product_model as productModel,
        t.product_name as productName,
        t.manufacturer as manufacturer,
        pc.category_name as productCategory,
        t.task_number as taskNumber,
        t.applicable_equipment_type as applicableEquipmentType,
        dt.device_type_name as deviceTypeName,
        t.program_number as programNumber,
        t.product_information1 as productInformation1,
        t.board_aging_capability as boardAgingCapability,
        t.total_board_aging_capability as totalBoardAgingCapability,
        t.codex_torch_creator_id as codexTorchCreatorId,
        t.codex_torch_updater as codexTorchUpdater,
        t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_create_datetime as codexTorchCreateDatetime,
        t.codex_torch_update_datetime as codexTorchUpdateDatetime,
        t.codex_torch_deleted as codexTorchDeleted
    </sql>
    <select id="selectCapabilityAssetPage"
            parameterType="com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO">
        select
        <include refid="Base_Column_List2"/>
        from capability_asset t
        left join product_category pc on t.product_category = pc.id
        left join device_type dt on t.applicable_equipment_type = dt.device_type_code
        <where>
            and t.codex_torch_deleted = '0'
            <if test="capabilityNumber != null and capabilityNumber != ''">
                and t.capability_number like concat('%', #{capabilityNumber} ,'%')
            </if>
            <if test="capabilityType != null and capabilityType != ''">
                and t.capability_type = #{capabilityType}
            </if>
            <if test="applicableTestType != null and applicableTestType != ''">
                and t.applicable_test_type = #{applicableTestType}
            </if>
            <if test="productModel != null and productModel != ''">
                and t.product_model = #{productModel}
            </if>
            <if test="productName != null and productName != ''">
                and t.product_name like concat('%', #{productName} ,'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and t.manufacturer like concat('%', #{manufacturer} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pc.category_name like concat('%', #{productCategory} ,'%')
            </if>
            <if test="taskNumber != null and taskNumber != ''">
                and t.task_number = #{taskNumber}
            </if>
            <if test="applicableEquipmentType != null and applicableEquipmentType != ''">
                and t.applicable_equipment_type like concat('%', #{applicableEquipmentType} ,'%')
            </if>
            <if test="programNumber != null and programNumber != ''">
                and t.program_number like concat('%', #{programNumber} ,'%')
            </if>
            <if test="productInformation1 != null and productInformation1 != ''">
                and t.product_information1 = #{productInformation1}
            </if>
            <if test="boardAgingCapability != null and boardAgingCapability != ''">
                and t.board_aging_capability = #{boardAgingCapability}
            </if>
            <if test="totalBoardAgingCapability != null and totalBoardAgingCapability != ''">
                and t.total_board_aging_capability = #{totalBoardAgingCapability}
            </if>
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
        order by t.codex_torch_create_datetime desc
    </select>
    <select id="selectOptionsByProductModel" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.product_model label,
        t.product_model value
        from product_management t
        WHERE t.product_model != ''
        and t.codex_torch_deleted = '0'
    </select>
    <select id="selectDataLinkageByProductModel" parameterType="String"
            resultType="java.util.Map">
        select
        t.product_name as productName,
        t.manufacturer as manufacturer,
        t.product_category as productCategory
        from product_management t
        WHERE t.product_model = #{product_model}
        and t.codex_torch_deleted = '0'
    </select>
    <select id="selectOptionsByTaskNumber" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.task_number label,
        t.task_number value
        from capability_development t
        WHERE t.task_number != ''
        and t.codex_torch_deleted = '0'
    </select>
    <select id="selectDataLinkageByTaskNumber" parameterType="String"
            resultType="java.util.Map">
        select
        t.applicable_equipment_type as applicableEquipmentType,
        t.program_number as programNumber,
        t.product_information1 as productInformation1,
        t.board_aging_capability as boardAgingCapability
        from capability_development t
        WHERE t.task_number = #{task_number}
        and t.codex_torch_deleted = '0'
    </select>

    <select id="selectCapabilityAssetList"
            parameterType="com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO">
        select
        <include refid="Base_Column_List2"/>
        from capability_asset t
        left join product_category pc on t.product_category = pc.id
        left join device_type dt on t.applicable_equipment_type = dt.device_type_code
        <where>
            and t.codex_torch_deleted = '0'
            <if test="capabilityNumber != null and capabilityNumber != ''">
                and t.capability_number like concat('%', #{capabilityNumber} ,'%')
            </if>
            <if test="capabilityType != null and capabilityType != ''">
                and t.capability_type = #{capabilityType}
            </if>
            <if test="applicableTestType != null and applicableTestType != ''">
                and t.applicable_test_type = #{applicableTestType}
            </if>
            <if test="productModel != null and productModel != ''">
                and t.product_model = #{productModel}
            </if>
            <if test="productName != null and productName != ''">
                and t.product_name like concat('%', #{productName} ,'%')
            </if>
            <if test="manufacturer != null and manufacturer != ''">
                and t.manufacturer like concat('%', #{manufacturer} ,'%')
            </if>
            <if test="productCategory != null and productCategory != ''">
                and pc.category_name like concat('%', #{productCategory} ,'%')
            </if>
            <if test="taskNumber != null and taskNumber != ''">
                and t.task_number = #{taskNumber}
            </if>
            <if test="applicableEquipmentType != null and applicableEquipmentType != ''">
                and t.applicable_equipment_type like concat('%', #{applicableEquipmentType} ,'%')
            </if>
            <if test="programNumber != null and programNumber != ''">
                and t.program_number like concat('%', #{programNumber} ,'%')
            </if>
            <if test="productInformation1 != null and productInformation1 != ''">
                and t.product_information1 = #{productInformation1}
            </if>
            <if test="boardAgingCapability != null and boardAgingCapability != ''">
                and t.board_aging_capability = #{boardAgingCapability}
            </if>
            <if test="totalBoardAgingCapability != null and totalBoardAgingCapability != ''">
                and t.total_board_aging_capability = #{totalBoardAgingCapability}
            </if>
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectCapabilityAssetListByIds"
            resultType="com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO">
        select
        <include refid="Base_Column_List"/>
        from capability_asset t
        <where>
            <if test="ids != null and ids.size > 0">
                t.id in
                <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectCapabilityAssetListByMultiParams"
            resultType="com.huatek.frame.modules.business.domain.vo.CapabilityAssetVO">
        select
        <include refid="Base_Column_List"/>
        from capability_asset t
        <where>
            and t.codex_torch_deleted = '0'
            <if test="queryParams != null and queryParams.size > 0">
                and (
                <foreach collection="queryParams" separator=" or " item="param">
                    (t.capability_type = #{param.verificationType} and t.product_model = #{param.productModel})
                </foreach>
                )
            </if>
        </where>
    </select>

    <!-- 根据产品型号和能力类型查询能力资产 -->
    <select id="selectByProductModelAndType"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskCapabilityVO">
        select
        t.id as id,
        t.capability_number as capabilityNumber,
        cd.operation_card as operationCard
        FROM capability_asset t left join capability_development cd on cd.capability_asset_number=t.capability_number
        WHERE t.codex_torch_deleted = '0' and cd.codex_torch_deleted = '0'
        AND t.product_model = #{productModel}
        AND t.capability_type = #{capabilityType}
    </select>
</mapper>