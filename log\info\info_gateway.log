2025-09-04 09:28:06,131 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-09-04 09:28:46,515 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-09-04 09:28:56,886 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-09-04 09:28:58,857 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menu/f52b1f9329cb4cd68d2fc660d9c2aac6
2025-09-04 09:31:34,315 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 09:31:34,320 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 09:31:34,474 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 09:31:34,690 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 09:31:34,930 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 09:31:35,023 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 09:31:35,064 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 09:31:35,100 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 09:31:35,135 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 09:31:35,265 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 09:31:38,150 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 09:31:38,587 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 09:31:38,768 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 09:31:39,062 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 09:31:39,142 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 09:32:25,751 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 09:44:50,400 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-04 09:44:50,404 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-04 09:44:50,410 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-04 09:44:50,433 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-04 09:44:50,433 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-04 09:44:50,433 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-04 09:44:50,492 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-04 09:44:50,550 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-04 09:44:50,715 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-04 09:44:51,166 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-04 09:44:51,238 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-04 09:44:51,287 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-04 09:44:51,345 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-04 09:44:51,427 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-04 09:44:51,912 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-04 09:44:56,783 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1963188238611034114
2025-09-04 09:44:57,517 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-04 09:44:57,532 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-04 09:44:57,532 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-04 09:44:57,532 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/all
2025-09-04 09:44:57,532 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-04 09:44:57,533 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-04 09:44:57,621 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1963188238611034114
2025-09-04 09:44:57,822 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-04 09:44:57,832 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-04 09:44:58,019 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-04 09:44:58,020 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-04 09:45:04,695 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/yes_or_no
2025-09-04 09:51:10,207 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 09:51:10,216 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 09:51:10,273 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 09:51:10,415 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 09:51:17,758 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-04 09:51:17,803 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-04 09:51:17,809 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-04 09:51:17,814 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-04 09:51:17,832 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-04 09:51:17,876 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-04 09:51:17,883 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-04 09:51:17,901 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-04 09:51:17,901 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-04 09:51:18,135 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-04 09:51:18,283 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-04 09:51:18,408 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-04 09:51:18,458 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-04 09:51:18,511 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-04 09:51:18,567 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-04 09:51:19,615 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-04 10:04:03,424 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 10:04:03,433 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:04:03,571 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:04:03,741 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 10:04:03,858 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:04:03,957 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:04:03,991 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:04:04,024 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:04:04,055 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 10:04:04,090 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:04:10,854 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:13:04,969 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 10:13:04,976 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 10:13:05,006 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 10:13:05,120 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 10:13:09,037 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 10:13:09,047 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-04 10:13:09,048 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:13:09,166 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:13:09,276 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 10:13:09,373 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:13:09,527 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:13:09,681 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:13:09,740 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:13:09,844 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 10:13:09,889 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:15:19,929 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:18:42,897 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 10:18:42,897 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:18:42,905 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-04 10:18:42,905 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-04 10:18:42,905 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-04 10:18:42,918 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-04 10:18:43,302 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-04 10:18:43,437 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:18:43,607 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-04 10:18:43,676 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-04 10:18:43,723 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:19:44,341 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:19:44,359 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 10:19:44,479 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:19:44,706 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 10:19:44,899 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:19:44,952 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:19:44,985 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:19:45,023 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:19:45,062 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 10:19:46,271 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:19:48,469 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:19:48,470 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/detail/1963185021084909569
2025-09-04 10:19:48,579 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-04 10:19:48,675 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:19:48,744 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:19:48,826 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:19:59,732 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:20:35,219 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/deviceType/cascade/null
2025-09-04 10:20:35,219 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/DeviceType
2025-09-04 10:20:47,542 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/deviceType/detail/1963180628168060929
2025-09-04 10:20:47,701 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/deviceType/cascade/deviceTypeName
2025-09-04 10:21:24,424 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 10:21:24,434 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:21:24,520 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:21:24,634 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 10:21:24,798 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:21:24,945 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:21:25,085 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:21:25,160 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:21:25,209 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 10:21:25,256 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:24:26,259 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-09-04 10:24:26,259 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-09-04 10:24:26,281 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceType
2025-09-04 10:24:26,283 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EquipmentInventory
2025-09-04 10:24:26,283 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:24:26,305 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-09-04 10:24:26,363 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-04 10:24:26,373 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-04 10:24:26,411 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-09-04 10:24:26,738 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-09-04 10:24:26,824 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-04 10:24:27,007 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:24:27,118 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-04 10:24:27,176 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-04 10:24:27,227 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/trace_information_d0
2025-09-04 10:28:25,388 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1956326082263269378
2025-09-04 10:28:25,704 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-09-04 10:28:28,325 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-04 10:28:28,328 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-04 10:28:28,331 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-04 10:28:28,334 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:28:28,342 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-04 10:28:28,344 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-09-04 10:28:28,383 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-04 10:28:28,406 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-04 10:28:28,511 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-04 10:28:28,626 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:28:28,665 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-04 10:28:45,672 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-09-04 10:31:16,282 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 10:31:16,290 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 10:31:16,318 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 10:31:16,640 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 10:31:18,939 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-04 10:31:18,963 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-09-04 10:31:18,968 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-09-04 10:31:18,970 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:31:18,988 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-04 10:31:18,989 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceType
2025-09-04 10:31:19,036 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-04 10:31:19,081 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/EquipmentInventory
2025-09-04 10:31:19,095 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/equipmentInventoryList
2025-09-04 10:31:19,260 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_d0
2025-09-04 10:31:19,663 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/manufacturer
2025-09-04 10:31:19,997 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-04 10:31:20,067 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:31:20,130 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-04 10:31:20,176 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-04 10:31:20,232 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/trace_information_d0
2025-09-04 10:35:04,533 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/detail/1956326082263269378
2025-09-04 10:35:15,955 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/latestTraceInfo
2025-09-04 10:35:17,384 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-04 10:35:17,388 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:35:17,394 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findUsers
2025-09-04 10:35:17,394 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-04 10:35:17,395 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-04 10:35:17,395 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-04 10:35:17,465 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:35:17,502 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_status
2025-09-04 10:35:17,802 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/trace_information_d0
2025-09-04 10:35:18,290 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/deviceTypeCode
2025-09-04 10:35:18,339 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/equipmentInventory/optionsList/belongingGroup
2025-09-04 10:36:51,308 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 10:36:51,320 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:36:51,485 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:36:51,653 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 10:36:51,849 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:36:51,954 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:36:52,163 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:36:52,361 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:36:52,448 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 10:36:52,548 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:50:19,169 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 10:50:19,184 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 10:50:19,274 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 10:50:19,506 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 10:50:25,770 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 10:50:26,121 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:50:26,216 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 10:50:26,398 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:50:26,453 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:50:26,497 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:50:26,548 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:51:05,056 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:51:06,107 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:51:07,161 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:51:08,838 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:51:19,678 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 10:51:19,701 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 10:51:19,869 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 10:51:20,261 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 10:51:24,365 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 10:51:24,365 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:51:24,366 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-04 10:51:24,899 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:51:25,077 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 10:51:25,769 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:51:26,062 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:51:26,119 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:51:26,181 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:51:26,225 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 10:51:26,469 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:52:36,940 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-04 10:52:36,941 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/awaitingProductionOrder/optionsList/responsiblePerson
2025-09-04 10:52:36,941 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-04 10:52:36,942 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-04 10:52:36,942 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-04 10:52:37,246 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/orderNumber
2025-09-04 10:52:37,250 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-04 10:52:37,466 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/entrustedUnit
2025-09-04 10:52:37,612 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/productModel
2025-09-04 10:52:37,757 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-04 10:52:37,806 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-04 10:52:37,913 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-04 10:52:37,978 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-04 10:52:38,022 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-04 10:52:38,080 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_productionStage
2025-09-04 10:52:40,696 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-04 10:53:00,078 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 10:53:00,087 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 10:53:00,128 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 10:53:00,232 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 10:53:02,070 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/awaitingProductionOrder/optionsList/responsiblePerson
2025-09-04 10:53:02,075 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/outsource_reason
2025-09-04 10:53:02,078 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_screened
2025-09-04 10:53:02,081 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/productionOrder
2025-09-04 10:53:02,085 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-04 10:53:02,089 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-04 10:53:02,401 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/non_power_aging
2025-09-04 10:53:02,458 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/orderNumber
2025-09-04 10:53:02,718 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/entrustedUnit
2025-09-04 10:53:02,842 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/optionsList/productModel
2025-09-04 10:53:03,508 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-04 10:53:03,625 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-04 10:53:03,729 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-04 10:53:03,786 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-04 10:53:03,844 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-04 10:53:04,053 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 10:53:04,081 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 10:53:04,137 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_productionStage
2025-09-04 10:53:04,716 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:53:05,096 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 10:53:05,274 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:53:05,328 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:53:05,376 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:53:05,423 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 10:53:05,473 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 10:53:05,573 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 10:53:07,042 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/productionOrderList
2025-09-04 10:53:12,561 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:53:12,562 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/detail/1963430439052034049
2025-09-04 10:53:12,700 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-04 10:53:12,814 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:53:12,892 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:53:12,951 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:53:14,488 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 10:53:14,574 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-04 10:53:14,744 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 10:53:14,794 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 10:53:14,829 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 10:53:17,458 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/sourceOrderNumber
2025-09-04 10:53:19,631 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/linkageData/production_order/S202509080
2025-09-04 10:53:53,787 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/view/1963107359754715137
2025-09-04 10:53:53,790 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-04 10:53:53,790 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-04 10:53:53,796 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-04 10:53:53,796 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-04 10:53:53,797 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-04 10:53:53,886 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportRequirements
2025-09-04 10:53:53,914 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportFormat
2025-09-04 10:53:53,929 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/yes_or_no
2025-09-04 11:10:06,899 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 11:10:16,730 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 11:10:16,738 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 11:10:16,791 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 11:10:16,915 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 11:10:19,065 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-04 11:10:19,067 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 11:10:19,089 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 11:10:19,254 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 11:10:19,415 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 11:10:19,653 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 11:10:19,766 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 11:10:19,870 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 11:10:20,176 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 11:10:20,304 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 11:10:20,489 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 11:13:20,608 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-04 11:13:20,613 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-04 11:13:20,618 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-04 11:13:20,622 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-04 11:13:20,624 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-04 11:13:20,646 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-04 11:13:20,684 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-04 11:13:20,742 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-04 11:13:20,754 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-04 11:13:20,757 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-04 11:13:21,125 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-04 11:13:21,561 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-04 11:13:21,690 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-04 11:13:21,764 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-04 11:13:32,318 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 11:13:32,326 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 11:13:32,373 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 11:13:32,500 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 11:13:34,749 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-04 11:13:34,766 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-04 11:13:34,767 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-04 11:13:34,767 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-04 11:13:34,770 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-04 11:13:34,774 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-04 11:13:34,850 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-04 11:13:34,855 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-04 11:13:34,860 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-04 11:13:34,883 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-04 11:13:34,910 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-04 11:13:35,168 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-04 11:13:35,375 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-04 11:13:35,546 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-04 11:13:35,623 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-04 11:14:46,460 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-04 11:14:49,513 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-04 11:14:51,555 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1951192992299749376
2025-09-04 11:14:54,199 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-04 11:15:01,879 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1951192992299749376
2025-09-04 11:15:06,926 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 11:15:06,947 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 11:15:07,072 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 11:15:07,253 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 11:15:07,634 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 11:15:07,716 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 11:15:07,773 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 11:15:07,830 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 11:15:07,877 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 11:15:07,931 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 11:21:33,124 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 11:21:33,136 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 11:21:33,250 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 11:21:33,363 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 11:21:33,572 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 11:21:33,684 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 11:21:33,727 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 11:21:33,751 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 11:21:33,779 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 11:21:33,815 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 11:21:36,597 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-04 11:21:36,600 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-04 11:21:36,603 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-04 11:21:36,604 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-04 11:21:36,604 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-04 11:21:36,618 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-04 11:21:36,696 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-04 11:21:36,699 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-04 11:21:36,726 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-04 11:21:36,726 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/null
2025-09-04 11:21:36,990 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-04 11:21:37,211 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-04 11:21:37,256 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-04 11:21:37,292 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-04 11:21:48,566 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 11:21:48,568 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-04 11:21:48,568 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 11:21:48,569 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-04 11:21:48,569 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-04 11:21:48,586 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-04 11:21:48,694 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-04 11:21:48,818 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 11:21:48,891 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-04 11:21:49,070 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-04 11:21:49,163 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 13:13:34,741 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 13:13:34,762 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 13:13:34,835 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 13:13:34,982 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 13:13:35,739 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-04 13:13:35,769 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-04 13:13:35,814 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-04 13:13:36,326 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-04 13:13:40,497 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-04 13:13:40,501 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 13:13:40,518 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-04 13:13:40,521 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-04 13:13:40,521 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 13:13:40,523 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-04 13:13:40,552 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-04 13:13:40,872 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-04 13:13:40,983 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 13:13:42,679 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-04 13:13:42,690 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-04 13:13:44,610 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-04 13:13:44,610 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-04 13:13:44,698 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-04 13:13:44,699 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-04 13:13:44,816 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-04 13:13:44,911 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 13:13:44,989 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-04 13:13:45,109 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-04 13:13:45,283 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-04 13:13:45,359 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-04 13:13:45,800 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-04 13:13:46,094 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-04 13:40:29,960 INFO gateway [Thread-41] c.n.l.PollingServerListUpdater [PollingServerListUpdater.java : 53] Shutting down the Executor Pool for PollingServerListUpdater
2025-09-04 13:40:30,177 INFO gateway [SpringContextShutdownHook] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 218] Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-09-04 13:40:30,201 INFO gateway [SpringContextShutdownHook] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 94] De-registering from Nacos Server now...
2025-09-04 13:40:30,223 INFO gateway [SpringContextShutdownHook] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 114] De-registration finished.
2025-09-04 13:40:36,726 INFO gateway [SpringContextShutdownHook] c.a.n.c.identify.CredentialWatcher [CredentialWatcher.java : 105] [null] CredentialWatcher is stopped
2025-09-04 13:40:36,729 INFO gateway [SpringContextShutdownHook] c.a.n.c.identify.CredentialService [CredentialService.java : 98] [null] CredentialService is freed
2025-09-04 13:41:14,304 INFO gateway [main] com.huatek.frame.GatewayApplication [SpringApplication.java : 655] The following profiles are active: dev
2025-09-04 13:41:16,392 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 249] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-04 13:41:16,402 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 127] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-04 13:41:16,503 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 187] Finished Spring Data repository scanning in 34ms. Found 0 Redis repository interfaces.
2025-09-04 13:41:17,094 INFO gateway [main] o.s.cloud.context.scope.GenericScope [GenericScope.java : 295] BeanFactory id=64f4209a-d1ff-3528-9ea5-7ff5c377b0aa
2025-09-04 13:41:17,460 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'redisConfig' of type [com.huatek.frame.gate.conf.RedisConfig$$EnhancerBySpringCGLIB$$67f6ff24] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 13:41:18,357 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 13:41:18,526 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 13:41:18,543 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 13:41:18,550 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 13:41:18,562 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'deferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-04 13:41:20,752 INFO gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-04 13:41:20,772 INFO gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-04 13:41:26,421 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [After]
2025-09-04 13:41:26,421 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Before]
2025-09-04 13:41:26,422 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Between]
2025-09-04 13:41:26,424 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Cookie]
2025-09-04 13:41:26,425 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Header]
2025-09-04 13:41:26,426 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Host]
2025-09-04 13:41:26,426 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Method]
2025-09-04 13:41:26,427 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Path]
2025-09-04 13:41:26,427 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Query]
2025-09-04 13:41:26,427 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [ReadBody]
2025-09-04 13:41:26,428 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [RemoteAddr]
2025-09-04 13:41:26,428 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Weight]
2025-09-04 13:41:26,429 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-09-04 13:41:27,183 INFO gateway [main] c.a.c.s.SentinelWebFluxAutoConfiguration [SentinelWebFluxAutoConfiguration.java : 91] [Sentinel Starter] register Sentinel SentinelWebFluxFilter
2025-09-04 13:41:27,275 INFO gateway [main] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-09-04 13:41:32,823 INFO gateway [main] o.s.b.w.e.netty.NettyWebServer [NettyWebServer.java : 109] Netty started on port(s): 8881
2025-09-04 13:41:36,269 INFO gateway [main] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 75] nacos registry, jx-mes jx-mes-gateway 192.168.56.1:8881 register finished
2025-09-04 13:41:36,453 INFO gateway [main] com.huatek.frame.GatewayApplication [StartupInfoLogger.java : 61] Started GatewayApplication in 51.114 seconds (JVM running for 52.855)
2025-09-04 13:41:36,551 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway+jx-mes
2025-09-04 13:41:36,554 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway, group=jx-mes, cnt=1
2025-09-04 13:41:36,586 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway-dev.yml+jx-mes
2025-09-04 13:41:36,587 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway-dev.yml, group=jx-mes, cnt=1
2025-09-04 13:41:36,593 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway.yml+jx-mes
2025-09-04 13:41:36,594 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway.yml, group=jx-mes, cnt=1
